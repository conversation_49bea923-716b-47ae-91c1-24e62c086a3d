<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>car-market-job</artifactId>
        <groupId>com.ctrip.car.market</groupId>
        <version>1.0.14</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>car-market-job-repository</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.car.osd.framework</groupId>
            <artifactId>common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>annotations</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.osd.framework</groupId>
            <artifactId>dal</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>car-market-job-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.customer</groupId>
            <artifactId>customer-common</artifactId>
        </dependency>

    </dependencies>
</project>
