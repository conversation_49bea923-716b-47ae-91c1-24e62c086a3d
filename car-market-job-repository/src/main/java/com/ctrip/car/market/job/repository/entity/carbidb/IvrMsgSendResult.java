package com.ctrip.car.market.job.repository.entity.carbidb;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2025-08-09
 */
@Entity
@Database(name = "carbidb_dalcluster")
@Table(name = "ivr_msg_send_result")
public class IvrMsgSendResult implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 业务线⾃定义消息id
     */
    @Column(name = "customEventID")
    @Type(value = Types.VARCHAR)
    private String customEventID;

    /**
     * 触达平台配置id
     */
    @Column(name = "taskGroupScene")
    @Type(value = Types.VARCHAR)
    private String taskGroupScene;

    /**
     * 触达平台消息类型 wechatTemplateMessage 微信公众号模板消息 appPush app推送 qunarSms 短信 wechatAppletsTemplateMessage 微信⼩程序模板消息 enterpriseWechatTemplateMessage 企微消息 stationLetter 站内信
     */
    @Column(name = "templateType")
    @Type(value = Types.VARCHAR)
    private String templateType;

    /**
     * 触达平台任务组id
     */
    @Column(name = "taskGroupId")
    @Type(value = Types.VARCHAR)
    private String taskGroupId;

    /**
     * 触达平台任务id
     */
    @Column(name = "taskId")
    @Type(value = Types.VARCHAR)
    private String taskId;

    /**
     * 调下游消息接⼝，返回的结果： true 成功（不⼀定到端，仅仅表⽰接⼝返回成功） false 失败
     */
    @Column(name = "success")
    @Type(value = Types.VARCHAR)
    private String success;

    /**
     * data.success=false时，失败原因描
     */
    @Column(name = "result")
    @Type(value = Types.VARCHAR)
    private String result;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomEventID() {
        return customEventID;
    }

    public void setCustomEventID(String customEventID) {
        this.customEventID = customEventID;
    }

    public String getTaskGroupScene() {
        return taskGroupScene;
    }

    public void setTaskGroupScene(String taskGroupScene) {
        this.taskGroupScene = taskGroupScene;
    }

    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    public String getTaskGroupId() {
        return taskGroupId;
    }

    public void setTaskGroupId(String taskGroupId) {
        this.taskGroupId = taskGroupId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getSuccess() {
        return success;
    }

    public void setSuccess(String success) {
        this.success = success;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}
