package com.ctrip.car.market.job.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.math.BigDecimal;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2025-09-03
 */
@Entity
@Database(name = "carmarketingdb_dalcluster")
@Table(name = "seo_comment_score")
public class SeoCommentScore implements DalPojo {

    /**
     * 自增id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 类型 全部：totle 国家：country 省份：province 城市：city
     */
    @Column(name = "itemType")
    @Type(value = Types.VARCHAR)
    private String itemType;

    /**
     * id
     */
    @Column(name = "itemId")
    @Type(value = Types.BIGINT)
    private Long itemId;

    /**
     * 点评分
     */
    @Column(name = "score")
    @Type(value = Types.DECIMAL)
    private BigDecimal score;

    /**
     * 点评子项评分
     */
    @Column(name = "subItemScore")
    @Type(value = Types.VARCHAR)
    private String subItemScore;

    /**
     * 点评总数
     */
    @Column(name = "totalCount")
    @Type(value = Types.INTEGER)
    private Integer totalCount;

    /**
     * 状态 0有效 1无效
     */
    @Column(name = "status")
    @Type(value = Types.INTEGER)
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public String getSubItemScore() {
        return subItemScore;
    }

    public void setSubItemScore(String subItemScore) {
        this.subItemScore = subItemScore;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}
