package com.ctrip.car.market.job.repository.service;

import com.ctrip.car.market.job.repository.entity.*;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.base.SQLResult;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.List;

@Repository
public class MarketDBService {

    private final DalTableOperations<Channel> channelOperations = DalOperationsFactory.getDalTableOperations(Channel.class);

    private final DalTableOperations<ChannelNumber> channelNumberOperations = DalOperationsFactory.getDalTableOperations(ChannelNumber.class);

    private final DalTableOperations<CpnUsecouponinfo> useCouponOperations = DalOperationsFactory.getDalTableOperations(CpnUsecouponinfo.class);

    private final DalTableOperations<MktapiToken> mktApiTokenOperations = DalOperationsFactory.getDalTableOperations(MktapiToken.class);

    private final DalTableOperations<AdQunarBanner> adQunarBrandOperations = DalOperationsFactory.getDalTableOperations(AdQunarBanner.class);

    private final DalTableOperations<ShopStore> shopStoreOperations = DalOperationsFactory.getDalTableOperations(ShopStore.class);

    private final DalTableOperations<SeoHotInformation> seoHotInformationOperations = DalOperationsFactory.getDalTableOperations(SeoHotInformation.class);

    private final DalTableOperations<SeoHotCountryinfo> seoHotCountryOperations = DalOperationsFactory.getDalTableOperations(SeoHotCountryinfo.class);

    private final DalTableOperations<SeoHotCityinfo> seoHotCityOperations = DalOperationsFactory.getDalTableOperations(SeoHotCityinfo.class);

    private final DalTableOperations<SeoHotDestinatioinfo> seoPoiOperations = DalOperationsFactory.getDalTableOperations(SeoHotDestinatioinfo.class);

    private final DalTableOperations<AlipaySendmessageRecord> alipayMessageOperations = DalOperationsFactory.getDalTableOperations(AlipaySendmessageRecord.class);

    private final DalTableOperations<AlipaySubscribeTemplateinfo> alipayTemplateOperations = DalOperationsFactory.getDalTableOperations(AlipaySubscribeTemplateinfo.class);

    private final DalTableOperations<AlipaySubscribeUserInfo> alipaySubscribeOperations = DalOperationsFactory.getDalTableOperations(AlipaySubscribeUserInfo.class);

    private final DalTableOperations<CarKalabCity> kalabCityOperations = DalOperationsFactory.getDalTableOperations(CarKalabCity.class);

    private final DalTableOperations<SeoHotVendor> seoHotVendorOperations = DalOperationsFactory.getDalTableOperations(SeoHotVendor.class);

    private final DalTableOperations<SeoHotVendorCity> seoHotVendorCityOperations = DalOperationsFactory.getDalTableOperations(SeoHotVendorCity.class);

    private final DalTableOperations<SeoHotVendorInformation> seoHotVendorInformationOperations = DalOperationsFactory.getDalTableOperations(SeoHotVendorInformation.class);

    private final DalTableOperations<SeoVendorCommentScore> seoVendorCommentScoreOperations = DalOperationsFactory.getDalTableOperations(SeoVendorCommentScore.class);

    private final DalTableOperations<MktTaskAssistInfo> mktTaskAssistInfoOperations = DalOperationsFactory.getDalTableOperations(MktTaskAssistInfo.class);

    private final DalTableOperations<MktWalletCashbackInfo> mktWalletCashbackInfoOperations = DalOperationsFactory.getDalTableOperations(MktWalletCashbackInfo.class);

    private final DalTableOperations<MktTaskUserInfo> mktTaskUserInfoOperations = DalOperationsFactory.getDalTableOperations(MktTaskUserInfo.class);

    private final DalTableOperations<SeoHotProvinceinfo> seoHotProvinceOperations = DalOperationsFactory.getDalTableOperations(SeoHotProvinceinfo.class);

    private final DalTableOperations<SeoCommentScore> seoCommentScoreOperations = DalOperationsFactory.getDalTableOperations(SeoCommentScore.class);


    public int deleteChannel(Channel channel) throws SQLException {
        return channelOperations.delete(new DalHints(), channel);
    }

    public int updateChannel(Channel channel) throws SQLException {
        return channelOperations.update(new DalHints(), channel);
    }

    public int insertChannel(Channel channel) throws SQLException {
        return channelOperations.insert(new DalHints(), channel);
    }

    public List<Channel> queryChannel(Channel sample) throws SQLException {
        return channelOperations.queryBy(sample, new DalHints());
    }

    public List<ChannelNumber> queryChannelNumber(ChannelNumber sample) throws SQLException {
        return channelNumberOperations.queryBy(sample, new DalHints());
    }

    public int deleteChannelNumber(ChannelNumber channelNumber) throws SQLException {
        return channelNumberOperations.delete(new DalHints(), channelNumber);
    }

    public int insertChannelNumberWithId(ChannelNumber channelNumber) throws SQLException {
        DalHints hints = new DalHints().enableIdentityInsert();
        return channelNumberOperations.insert(hints, channelNumber);
    }

    public int updateChannelNumber(ChannelNumber channelNumber) throws SQLException {
        return channelNumberOperations.update(new DalHints(), channelNumber);
    }

    public List<CpnUsecouponinfo> queryUseCoupon(CpnUsecouponinfo sample) throws SQLException {
        return useCouponOperations.queryBy(sample, new DalHints());
    }

    public String queryUseCoupon(Long orderId) throws SQLException {
        String sql = "select CouponCode from cpn_usecouponinfo where OrderID = ? order by DataChange_LastTime desc limit 1;";
        return useCouponOperations.queryFirst(sql, new DalHints(), SQLResult.type(String.class), orderId);
    }

    public int updateMktApiToken(MktapiToken mktapiToken) throws SQLException {
        return mktApiTokenOperations.update(new DalHints(), mktapiToken);
    }

    public List<MktapiToken> queryMktApiToken(MktapiToken sample) throws SQLException {
        return mktApiTokenOperations.queryBy(sample, new DalHints());
    }

    public List<AdQunarBanner> queryValidByFrameAndImpId(Integer frame, String impId) throws SQLException {
        String sql = "SELECT * FROM ad_qunar_banner WHERE frame = ? AND impId = ? AND activeStatus = true ORDER BY datachange_lasttime DESC";
        return adQunarBrandOperations.query(sql, new DalHints(), frame, impId);
    }

    public int insertAdQunar(AdQunarBanner adQunarBanner) throws SQLException {
        return adQunarBrandOperations.insert(new DalHints(), adQunarBanner);
    }

    public int updateAdQunar(AdQunarBanner adQunarBanner) throws SQLException {
        return adQunarBrandOperations.update(new DalHints(), adQunarBanner);
    }

    public int updateStatueByFrameAndImpId(Boolean statue, Integer frame, String impId) throws SQLException {
        String sql = "UPDATE ad_qunar_banner SET activeStatus = ? WHERE frame = ? AND impId = ?";
        return adQunarBrandOperations.update(sql, new DalHints(), statue, frame, impId);
    }

    public List<ShopStore> queryShopStoreByPage(Integer no, Integer size) throws SQLException {
        return shopStoreOperations.query("select * from shop_store where isActive = 1 order by id limit ?,?", new DalHints(), no, size);
    }

    public int insertShopStore(ShopStore shopStore) throws SQLException {
        return shopStoreOperations.insert(new DalHints(), shopStore);
    }

    public int updateShopStore(ShopStore shopStore) throws SQLException {
        return shopStoreOperations.update(new DalHints(), shopStore);
    }

    public List<SeoHotCountryinfo> queryAllHotCountry() throws Exception {
        SeoHotCountryinfo sample = new SeoHotCountryinfo();
        sample.setStatus(0);
        return seoHotCountryOperations.queryBy(sample, new DalHints());
    }

    public List<SeoHotProvinceinfo> queryAllSeoHotProvince() throws Exception {
        SeoHotProvinceinfo sample = new SeoHotProvinceinfo();
        sample.setStatus(0);
        return seoHotProvinceOperations.queryBy(sample, new DalHints());
    }

    public List<SeoHotCityinfo> queryAllHotCity() throws SQLException {
        SeoHotCityinfo sample = new SeoHotCityinfo();
        sample.setStatus(0);
        return seoHotCityOperations.queryBy(sample, new DalHints());
    }

    public List<SeoHotDestinatioinfo> queryAllHotPoi() throws SQLException {
        SeoHotDestinatioinfo sample = new SeoHotDestinatioinfo();
        sample.setStatus(0);
        return seoPoiOperations.queryBy(sample, new DalHints());
    }

    public List<SeoHotInformation> queryALLInformation() throws SQLException {
        SeoHotInformation sample = new SeoHotInformation();
        sample.setStatus(0);
        return seoHotInformationOperations.queryBy(sample, new DalHints());
    }

    public List<SeoHotVendor> queryAllHotVendor() throws SQLException {
        SeoHotVendor sample = new SeoHotVendor();
        sample.setStatus(0);
        return seoHotVendorOperations.queryBy(sample, new DalHints());
    }

    public List<SeoHotVendorCity> queryAllHotVendorCity() throws SQLException {
        SeoHotVendorCity sample = new SeoHotVendorCity();
        sample.setStatus(0);
        return seoHotVendorCityOperations.queryBy(sample, new DalHints());
    }

    public List<SeoVendorCommentScore> queryAllVendorScore() throws Exception {
        SeoVendorCommentScore sample = new SeoVendorCommentScore();
        sample.setStatus(0);
        return seoVendorCommentScoreOperations.queryBy(sample, new DalHints());
    }

    public List<SeoHotVendorInformation> queryALlVendorInformation() throws Exception {
        List<SeoHotVendorInformation> result = Lists.newArrayList();
        long id = 0L;
        List<SeoHotVendorInformation> temp;
        do {
            temp = seoHotVendorInformationOperations.query("select * from seo_hot_vendor_information where status=0 and id>? order by id limit 5000", new DalHints(), id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(SeoHotVendorInformation::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }


    public int insertMessage(String uid, String templateId) {
        AlipaySendmessageRecord alipaySendmessageRecord = new AlipaySendmessageRecord();
        alipaySendmessageRecord.setUid(uid);
        alipaySendmessageRecord.setTemplateId(templateId);
        try {
            return alipayMessageOperations.insert(new DalHints(), alipaySendmessageRecord);
        } catch (Exception e) {
            return 0;
        }
    }

    public AlipaySendmessageRecord queryMessage(String uid, String templateId) throws SQLException {
        String sql = "select * from alipay_sendmessage_record where uid=? and templateId=? order by datachange_lasttime desc limit 1;";
        return alipayMessageOperations.queryFirst(sql, new DalHints(), SQLResult.type(AlipaySendmessageRecord.class), uid, templateId);
    }

    public AlipaySubscribeTemplateinfo queryMessageTemplate(Integer type) throws SQLException {
        AlipaySubscribeTemplateinfo sample = new AlipaySubscribeTemplateinfo();
        sample.setTemplateType(type);
        sample.setStatus(0);
        List<AlipaySubscribeTemplateinfo> values = alipayTemplateOperations.queryBy(sample, new DalHints());
        return CollectionUtils.isNotEmpty(values) ? values.get(0) : null;
    }

    public void insertSubscribeUser(AlipaySubscribeUserInfo userInfo) throws SQLException {
        alipaySubscribeOperations.insert(new DalHints(), userInfo);
    }

    public List<AlipaySubscribeUserInfo> querySubscribeUser(String templateId) throws SQLException {
        List<AlipaySubscribeUserInfo> result = Lists.newArrayList();
        long id = 0L;
        List<AlipaySubscribeUserInfo> temp;
        do {
            temp = alipaySubscribeOperations.query("select * from alipay_subscribe_userinfo where templateId=? and id>? order by id limit 5000", new DalHints(), templateId, id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(AlipaySubscribeUserInfo::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public List<CarKalabCity> queryAllOsdCity() throws SQLException {
        List<CarKalabCity> result = Lists.newArrayList();
        long id = 0L;
        List<CarKalabCity> temp;
        do {
            temp = kalabCityOperations.query("select * from car_kalab_city where id>? and businessType='osd' and tripValidStatus=1 order by Id limit 2000", new DalHints(), id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(CarKalabCity::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public boolean updateSeoHotCity(SeoHotCityinfo sample) throws SQLException {
        return seoHotCityOperations.update(new DalHints(), sample) > 0;
    }

    public boolean insertVendorInformation(SeoHotVendorInformation value) throws SQLException {
        return seoHotVendorInformationOperations.insert(new DalHints(), value) > 0;
    }

    public boolean updateVendorInformation(SeoHotVendorInformation value) throws SQLException {
        return seoHotVendorInformationOperations.update(new DalHints(), value) > 0;
    }

    public boolean insertVendorCommentScore(SeoVendorCommentScore value) throws SQLException {
        return seoVendorCommentScoreOperations.insert(new DalHints(), value) > 0;
    }

    public boolean updateVendorCommentScore(SeoVendorCommentScore value) throws SQLException {
        return seoVendorCommentScoreOperations.update(new DalHints(), value) > 0;
    }

    public boolean insertSeoCommentScore(SeoCommentScore value) throws SQLException {
        return seoCommentScoreOperations.insert(new DalHints(), value) > 0;
    }

    public  boolean updateSeoCommentScore(SeoCommentScore value) throws SQLException {
        return seoCommentScoreOperations.update(new DalHints(), value) > 0;
    }

    public List<SeoCommentScore> queryAllSeoCommentScore() throws SQLException {
        SeoCommentScore sample = new SeoCommentScore();
        sample.setStatus(0);
        return seoCommentScoreOperations.queryBy(sample, new DalHints());
    }

    public List<MktTaskAssistInfo> queryWaitCashbackByPage(Integer pageIndex, Integer pageSize, String cashbackEndTime) throws SQLException {
        StringBuilder sql = new StringBuilder("select * from mkt_task_assist_info");
        sql.append(" where cashbackStatus = 1  and cashbackTime <= ?");
        sql.append(" limit ?,").append(pageSize);
        return mktTaskAssistInfoOperations.query(sql.toString(), new DalHints(),cashbackEndTime,pageIndex);
    }

    public boolean updateMktTaskAssistInfo(MktTaskAssistInfo mktTaskAssistInfo) throws SQLException {
        return mktTaskAssistInfoOperations.update(new DalHints(),mktTaskAssistInfo) > 0 ;
    }

    public boolean insertMktWalletCashbackInfo(MktWalletCashbackInfo mktWalletCashbackInfo) throws SQLException {
        return mktWalletCashbackInfoOperations.insert(new DalHints().setIdentityBack(),mktWalletCashbackInfo) > 0 ;
    }

    public List<MktWalletCashbackInfo> selectMktWalletCashbackInfoByTaskAssistId(Long  taskAssistId) throws SQLException {
        MktWalletCashbackInfo sample = new MktWalletCashbackInfo();
        sample.setTaskAssistId(taskAssistId);
        return mktWalletCashbackInfoOperations.queryBy(sample, new DalHints());
    }

    public boolean updateMktWalletCashbackInfo(MktWalletCashbackInfo mktWalletCashbackInfo) throws SQLException {
        return mktWalletCashbackInfoOperations.update(new DalHints(),mktWalletCashbackInfo) > 0 ;
    }

    public List<MktTaskUserInfo> queryTaskUserInfo(Long projectId,Long taskId,Long orderId) throws SQLException {
        MktTaskUserInfo sample = new MktTaskUserInfo();
        sample.setProjectId(projectId);
        sample.setTaskId(taskId);
        sample.setOrderId(orderId);
        return mktTaskUserInfoOperations.queryBy(sample, new DalHints());
    }
}
