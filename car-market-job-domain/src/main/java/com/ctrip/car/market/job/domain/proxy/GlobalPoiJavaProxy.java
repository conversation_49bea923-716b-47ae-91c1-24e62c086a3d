package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.car.osd.basicdataservice.dto.Airport;
import com.ctrip.car.osd.basicdataservice.dto.DataSource;
import com.ctrip.car.osd.basicdataservice.dto.GetAirportsRequestType;
import com.ctrip.car.osd.basicdataservice.dto.GetAirportsResponseType;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.gs.globalpoi.soa.contract.*;
import com.ctrip.igt.RequestHeader;
import com.ctriposs.baiji.rpc.common.types.BaseRequest;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class GlobalPoiJavaProxy {
    private final ILog log = LogManager.getLogger(GlobalPoiJavaProxy.class);

    private GlobalPoiJavaClient client = GlobalPoiJavaClient.getInstance();

    private static final Integer MAX_NUMBER = 500;

    public Map<String, Long> getPoiIdByPoiCode(List<String> poiCode, BizCodeEnum bizCodeEnum) {
        Map<String, Long> result = new HashMap<>();
        try {
            int limit = (poiCode.size() + MAX_NUMBER - 1) / MAX_NUMBER;
            Stream.iterate(0, n -> n + 1).limit(limit).forEach(i -> {
                GetPoiIdsByBizCodeRequestType requestType = new GetPoiIdsByBizCodeRequestType();
                requestType.setBizCode(poiCode.stream().skip((long) i * MAX_NUMBER).limit(MAX_NUMBER).collect(Collectors.toList()));
                requestType.setType(bizCodeEnum);
                GetPoiIdsByBizCodeResponseType poiIdsByBizCode = new GetPoiIdsByBizCodeResponseType();
                try {
                    poiIdsByBizCode = client.getPoiIdsByBizCode(requestType);
                } catch (Exception e) {
                    log.warn("query poiId failed", e);
                }
                if (poiIdsByBizCode != null && !poiIdsByBizCode.getResult().isEmpty()) {
                    result.putAll(poiIdsByBizCode.getResult());
                }
            });
            return result;
        } catch (Exception e) {
            log.warn("query poiId failed", e);
            return result;
        }
    }
    public List<GlobalInfo> getScenerySpot(String code, String locale) {
        // 创建请求对象并设置地点ID
        PoiGlobalizationDetailRequestType requestType=new PoiGlobalizationDetailRequestType();
        requestType.setPoiIds(Lists.newArrayList(Long.valueOf(code)));
        requestType.setLang(locale);
        try {
            // 调用服务获取地点详情
            PoiGlobalizationDetailResponseType poiDetail = client.poiGlobalizationDetail(requestType);
            return poiDetail.getResult();
        } catch (Exception e) {
            log.error("Error fetching scenery spot details for code: {}", code);
            return Collections.emptyList(); // 返回空列表以避免返回 null
        }
    }


    public Long getPoiIdByCode(String poiCode) {
        GetPoiIdsByBizCodeRequestType requestType = new GetPoiIdsByBizCodeRequestType();
        requestType.setType(BizCodeEnum.THREECODE_AIRPORT);
        requestType.setBizCode(Lists.newArrayList(poiCode));
        try {
            GetPoiIdsByBizCodeResponseType poiIdsByBizCode = client.getPoiIdsByBizCode(requestType);
            return poiIdsByBizCode != null && poiIdsByBizCode.getResult() != null ? poiIdsByBizCode.getResult().get(poiCode) : null;
        } catch (Exception e) {
            log.warn("query poiId failed", e);
        }
        return null;
    }


}
