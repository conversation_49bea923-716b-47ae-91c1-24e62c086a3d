package com.ctrip.car.market.job.domain.mq.mkt;


import com.ctrip.arch.distlock.DistributedLockService;
import com.ctrip.arch.distlock.redis.RedisDistributedLockService;
import com.ctrip.bdai.basebiz.data.ubt.avro.UBTDecode;
import com.ctrip.car.market.job.domain.dto.coupon.QunarUBTDateDTO;
import com.ctrip.car.market.job.domain.service.MktCouponHandleService;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.domain.utils.JsonUtils;
import com.ctrip.framework.ckafka.client.KafkaClientFactory;
import com.ctrip.framework.ckafka.client.env.EnvProviderRegistry;
import com.ctrip.framework.ckafka.codec.deserializer.HermesAvroDeserializer;
import com.ctrip.framework.ckafka.codec.deserializer.HermesTransparentAvroDeserializer;
import com.ctrip.framework.ckafka.codec.entity.HermesConsumerConfig;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

import static com.ctrip.car.market.job.domain.constant.CommonConstant.QUNAR_KAFKA_UBT_CONSUMER;
import static com.ctrip.car.market.job.domain.constant.CommonConstant.QUNAR_KAFKA_UBT_TOPIC;

@Component
public class MKTCarUBTListen {

    private static final ILog log = LogManager.getLogger(MKTCarUBTListen.class);


    //    @Bean
//    public DistributedLockService distributedLockService() {
//        try {
//            log.warn("MKTCarUBTListen", "11111");
//
//            final String offsetResetConfig =
//                    System.getenv().getOrDefault(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
//
//            Properties properties = new Properties();
//            properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, offsetResetConfig);
//            properties.put(
//                    ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getCanonicalName());
//            properties.put(
//                    ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,
//                    HermesTransparentAvroDeserializer.class.getCanonicalName());
//            String registryAddress = EnvProviderRegistry.getEnvProvider().getEnv().schemaRegistryAddress();
//            properties.put("schema.registry.url", registryAddress);
//
//            properties.put(
//                    HermesConsumerConfig.HERMES_MESSAGE_CLASS_CONFIG, "org.apache.avro.generic.GenericRecord");
//            Consumer<String, UBTDecode> consumer = KafkaClientFactory.newConsumer(QUNAR_KAFKA_UBT_TOPIC, QUNAR_KAFKA_UBT_CONSUMER, properties);
//            log.warn("MKTCarUBTListen", "22222");
//            while (true) {
//                ConsumerRecords<String, UBTDecode> records = consumer.poll(Duration.ofMillis(1000L));
//                for (ConsumerRecord<String, UBTDecode> record : records) {
//                    sendToQunar(record.value());
//
//                    log.warn("MKTCarUBTListen", "323333");
//                }
//            }
//        } catch (Exception ex) {
//            log.error("MKTCarUBTListen", ex);
//        }
//        return new RedisDistributedLockService("com.ctrip.car.market.job.domain.ivr.lock", 60, TimeUnit.SECONDS);
//
//    }
//    @Bean
//    public DistributedLockService distributedLockService() {
////        try {
////            log.info("MKTCarUBTListen", "11111");
////
////            final String offsetResetConfig =
////                    System.getenv().getOrDefault(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
////
////            Properties properties = new Properties();
////            properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, offsetResetConfig);
////            properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getCanonicalName());
////            properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, HermesAvroDeserializer.class.getCanonicalName());
////            properties.put(HermesConsumerConfig.HERMES_MESSAGE_CLASS_CONFIG, UBTDecode.class.getCanonicalName()); // 此值为你接收的类
////// 其他配置按需添加
////            String registryAddress = EnvProviderRegistry.getEnvProvider().getEnv().schemaRegistryAddress();
////            properties.put("schema.registry.url", registryAddress);
////                   Consumer<String, UBTDecode> consumer = KafkaClientFactory.newConsumer(QUNAR_KAFKA_UBT_TOPIC, QUNAR_KAFKA_UBT_CONSUMER, properties);
////            log.info("MKTCarUBTListen", "22222");
////            while (true) {
////                ConsumerRecords<String, UBTDecode> records = consumer.poll(Duration.ofMillis(1000L));
////                for (ConsumerRecord<String, UBTDecode> record : records) {
////                    try {
////                        log.info("MKTCarUBTListen", JsonUtils.toString(record.value()));
////
////                        sendToQunar(record.value());
////                    } catch (Exception e) {
////                        log.error("MKTCarUBTListen", e);
////                    }
////                }
////            }
////        } catch (Exception ex) {
////            log.error("MKTCarUBTListen", ex);
////        }
//        return new RedisDistributedLockService("com.ctrip.car.market.job.domain.ivr.lock", 60, TimeUnit.SECONDS);
//    }

//    @Autowired
//    MktCouponHandleService mktCouponHandleService;
//
//    public void sendToQunar(UBTDecode record) {
//        try {
//
//            QunarUBTDateDTO qunarUBTDateDTO = new QunarUBTDateDTO();
//            qunarUBTDateDTO.setAppId(record.getAppId().toString());
//            if(record.getDataInfo()!=null){
//                qunarUBTDateDTO.setUbtClickENName(record.getDataInfo().get("enName")+"");
//                qunarUBTDateDTO.setUbtClickName(record.getDataInfo().get("name")+"");
//            }
//            qunarUBTDateDTO.setDateType(1);
//            qunarUBTDateDTO.setDistributionChannelID(record.getChannelId() + "");
//            //qunarUBTDateDTO.setOrderID();
//            qunarUBTDateDTO.setSourceFrom("ISD_Q_APP");
//            qunarUBTDateDTO.setUserID(record.getUid()+"");
//            qunarUBTDateDTO.setUserName(record.getLoginName()+"");
//            qunarUBTDateDTO.setPageID(record.getPage()+"");
//            qunarUBTDateDTO.setKeyId(record.getKeyId() + "");
//            log.info("MKTCarUBTListen", "44444");
//            mktCouponHandleService.sendQMQ(qunarUBTDateDTO);
//            log.info("MKTCarUBTListen", "55555");
//        }catch (Exception ex)
//        {
//            log.warn("UBTDecode",ex);
//        }
//
//    }
}

