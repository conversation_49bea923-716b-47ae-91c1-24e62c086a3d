package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.car.osd.basicdataservice.dto.*;
import com.ctrip.igt.geo.interfaces.dto.PlaceDetailsDTO;
import com.ctrip.igt.geo.interfaces.message.QueryFuzzyAddressResponseType;
import com.ctrip.igt.geo.interfaces.message.QueryFuzzyAddressRequestType;
import com.ctrip.igt.RequestHeader;
import com.ctrip.igt.geo.interfaces.OchGeoServiceClient;
import com.ctrip.igt.geo.interfaces.message.QueryPlaceDetailsRequestType;
import com.ctrip.igt.geo.interfaces.message.QueryPlaceDetailsResponseType;
import com.ctriposs.baiji.rpc.common.types.BaseRequest;
import com.google.common.collect.Lists;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class IgtGeoServiceProxy {

    private final ILog log = LogManager.getLogger(IgtGeoServiceProxy.class);

    private final OchGeoServiceClient client = OchGeoServiceClient.getInstance();

    public QueryFuzzyAddressResponseType queryFuzzyAddress(Long cityId, String poiName){
        try {

            QueryFuzzyAddressRequestType requestType = new QueryFuzzyAddressRequestType();
            requestType.setCityCode(cityId.toString());
            requestType.setQuery(poiName);
            requestType.setCityLimit(true);
            requestType.setLimitCount(1);
            requestType.setRequestHeader(new RequestHeader());
            requestType.getRequestHeader().setSeverFrom("carrental");
            requestType.getRequestHeader().setLocale("zh-CN");
            return client.queryFuzzyAddress(requestType);
        } catch (Exception e) {
            log.warn("queryFuzzyAddress", e);
            return null;
        }
    }
    public PlaceDetailsDTO getTrainStation(String code, String locale) {
        QueryPlaceDetailsRequestType requestType = new QueryPlaceDetailsRequestType();
        requestType.setRequestHeader(new RequestHeader());
        requestType.getRequestHeader().setLocale(locale);
        requestType.setCarPlaceIds(Lists.newArrayList(code));
        try {
            QueryPlaceDetailsResponseType responseType = client.queryPlaceDetails(requestType);
            if (CollectionUtils.isNotEmpty(responseType.getPlaceDetails())) {
                return responseType.getPlaceDetails().get(0);
            }
            return null;
        } catch (Exception e) {
            log.warn("getPoiDetail", e);
            return null;
        }
    }

}