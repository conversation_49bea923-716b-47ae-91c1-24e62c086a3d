package com.ctrip.car.market.job.domain.mq.mkt;


import com.ctrip.bdai.basebiz.data.ubt.avro.UBTDecode;
import com.ctrip.car.market.job.domain.config.JobConfig;
import com.ctrip.car.market.job.domain.dto.coupon.QunarUBTDateDTO;
import com.ctrip.car.market.job.domain.service.MktCouponHandleService;
import com.ctrip.car.market.job.domain.utils.JsonUtils;
import com.ctrip.car.market.job.domain.utils.MapConverter;
import com.ctrip.car.market.job.domain.utils.Metrics;
import com.ctrip.framework.ckafka.client.KafkaClientFactory;
import com.ctrip.framework.ckafka.client.env.EnvProviderRegistry;
import com.ctrip.framework.ckafka.codec.deserializer.HermesAvroDeserializer;
import com.ctrip.framework.ckafka.codec.entity.HermesConsumerConfig;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.ctrip.car.market.job.domain.constant.CommonConstant.QUNAR_KAFKA_UBT_CONSUMER;
import static com.ctrip.car.market.job.domain.constant.CommonConstant.QUNAR_KAFKA_UBT_TOPIC;

@Component
public class QunarUBTListener3 implements ApplicationRunner {
    private static final ILog log = LogManager.getLogger(QunarUBTListener3.class);

    private final AtomicBoolean running = new AtomicBoolean(true);

    private Consumer<String, UBTDecode> consumer;

    @Autowired
    private MktCouponHandleService mktCouponHandleService;

    @Autowired
    private JobConfig jobConfig;


    @Override
    public void run(ApplicationArguments applicationArguments) throws Exception {
        CompletableFuture.runAsync(this::asyncRun);
    }

    public void asyncRun() {
        log.info("QunarUBTListener", "Starting Kafka consumer...");
        try {
            final String offsetResetConfig = System.getenv()
                    .getOrDefault(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");

            Properties properties = new Properties();
            properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, offsetResetConfig);
            properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG,
                    StringDeserializer.class.getCanonicalName());
            properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,
                    HermesAvroDeserializer.class.getCanonicalName());
            properties.put(HermesConsumerConfig.HERMES_MESSAGE_CLASS_CONFIG,
                    UBTDecode.class.getCanonicalName());

            String registryAddress = EnvProviderRegistry.getEnvProvider().getEnv().schemaRegistryAddress();
            properties.put("schema.registry.url", registryAddress);

            consumer = KafkaClientFactory.newConsumer(
                    QUNAR_KAFKA_UBT_TOPIC,
                    QUNAR_KAFKA_UBT_CONSUMER,
                    properties
            );

            log.info("QunarUBTListener", "Kafka consumer initialized successfully");

            while (running.get()) {
                try {
                    ConsumerRecords<String, UBTDecode> records = consumer.poll(Duration.ofMillis(1000L));
                    for (ConsumerRecord<String, UBTDecode> record : records) {
                        processRecord(record);
                    }
                } catch (Exception e) {
                    if (running.get()) {
                        log.error("QunarUBTListener", e);
                        Metrics.build()
                                .withTag("result", "pollError")
                                .withTag("mktQMQType", "QunarUBT")
                                .recordOne("QunarUBTListener");
                    }
                }
            }
        } catch (Exception e) {
            log.error("QunarUBTListener", e);
        } finally {
            if (consumer != null) {
                consumer.close();
                log.info("QunarUBTListener", "Kafka consumer closed");
            }
        }
    }

    private void processRecord(ConsumerRecord<String, UBTDecode> record) {
        try {
            Metrics.build()
                    .withTag("result", "received")
                    .withTag("mktQMQType", "QunarUBT")
                    .recordOne("QunarUBTListener");

            sendToQunar(record.value());
        } catch (Exception e) {
            log.error("QunarUBTListener", e);
            Metrics.build()
                    .withTag("result", "processError")
                    .withTag("mktQMQType", "QunarUBT")
                    .recordOne("QunarUBTListener");
        }
    }

    private void sendToQunar(UBTDecode record) {
        try {
            QunarUBTDateDTO qunarUBTDateDTO = new QunarUBTDateDTO();
            if (record.getDataInfo() != null) {
                log.warn("QunarUBTListener3", "Received UBT record: " + JsonUtils.toString(record.getDataInfo()));
                Map<String, String> result = MapConverter.toStringMap(record.getDataInfo());
                if (result.containsKey("distributionChannelId")) {
                    qunarUBTDateDTO.setDistributionChannelID(result.get("distributionChannelId"));
                }
                if (result.containsKey("name")) {
                    qunarUBTDateDTO.setUbtClickName(result.get("name"));
                }
                if (result.containsKey("enName")) {
                    qunarUBTDateDTO.setUbtClickENName(result.get("enName"));
                }
                if (result.containsKey("sourceFrom")) {
                    qunarUBTDateDTO.setSourceFrom(result.get("sourceFrom"));
                }

                if (result.containsKey("uId")) {
                    qunarUBTDateDTO.setUserID(result.get("uId"));
                }
                if (result.containsKey("userName")) {
                    qunarUBTDateDTO.setUserName(result.get("userName"));
                }
            }
            /////#####################################
            if (StringUtils.isNotBlank(record.getUid())) {
                qunarUBTDateDTO.setUserID(record.getUid().toString());
            }
            if (StringUtils.isNotBlank(record.getLoginName())) {
                qunarUBTDateDTO.setUserName(record.getLoginName().toString());

            }
            if (record.getPage() != null) {
                qunarUBTDateDTO.setPageID(record.getPage().toString());

            }
            if (record.getKey() != null) {
                qunarUBTDateDTO.setKeyId(record.getKey().toString());

            }
            if (record.getAppId() != null) {
                qunarUBTDateDTO.setAppId(record.getAppId().toString());

            }
            if (record.getBu() != null) {
                qunarUBTDateDTO.setBu(record.getBu().toString());
            }
            log.info("QunarUBTListener", JsonUtils.toString(qunarUBTDateDTO));
            // 应用过滤规则
            if (!applyFilterRules(qunarUBTDateDTO)) {
                return;
            }


            qunarUBTDateDTO.setDateType(1);
            mktCouponHandleService.sendQMQ(qunarUBTDateDTO);


            Metrics.build()
                    .withTag("result", "sendSuc")
                    .withTag("mktQMQType", "QunarUBT")
                    .recordOne("QunarUBTListener");
        } catch (Exception ex) {
            log.error("UBTDecode", ex);
            Metrics.build()
                    .withTag("result", "sendError")
                    .withTag("mktQMQType", "QunarUBT")
                    .recordOne("QunarUBTListener");
        }
    }

    private boolean applyFilterRules(QunarUBTDateDTO record) {

        if (StringUtils.isEmpty(record.getUserID())) {
            Metrics.build()
                    .withTag("result", "UidNullFilter")
                    .withTag("mktQMQType", "QunarUBT")
                    .recordOne("QunarUBTListener");
            return false;
        } else {
            Metrics.build()
                    .withTag("result", "UidOver")
                    .withTag("mktQMQType", "QunarUBT")
                    .recordOne("QunarUBTListener");
        }

        // 1. eName过滤
        if (!CollectionUtils.isEmpty(jobConfig.getCtripUBTClientEnames())) {
            String enName = record.getUbtClickENName();
            if (StringUtils.isNotEmpty(enName)) {
                if (jobConfig.getCtripUBTClientEnames().contains(enName)) {
                    Metrics.build()
                            .withTag("result", "eNameOver")
                            .withTag("mktQMQType", "QunarUBT")
                            .recordOne("QunarUBTListener");
                } else {
                    Metrics.build()
                            .withTag("result", "eNameFilter")
                            .withTag("mktQMQType", "QunarUBT")
                            .recordOne("QunarUBTListener");
                    return false;
                }
            } else {
                Metrics.build()
                        .withTag("result", "eNameNullFilter")
                        .withTag("mktQMQType", "QunarUBT")
                        .recordOne("QunarUBTListener");
                return false;
            }

        } else {
            Metrics.build()
                    .withTag("result", "eNameNotConfig")
                    .withTag("mktQMQType", "QunarUBT")
                    .recordOne("QunarUBTListener");
        }

        // 2. ChannelID过滤
        if (!CollectionUtils.isEmpty(jobConfig.getQappUBTChannelIds())) {
            String channelId = record.getDistributionChannelID();
            if (StringUtils.isNotEmpty(channelId)) {
                if (jobConfig.getQappUBTChannelIds().contains(channelId)) {

                    Metrics.build()
                            .withTag("result", "channelOver")
                            .withTag("mktQMQType", "QunarUBT")
                            .recordOne("QunarUBTListener");
                } else {
                    Metrics.build()
                            .withTag("result", "channelFilter")
                            .withTag("mktQMQType", "QunarUBT")
                            .recordOne("QunarUBTListener");
                    return false;
                }
            } else {
                Metrics.build()
                        .withTag("result", "channelNullFilter")
                        .withTag("mktQMQType", "QunarUBT")
                        .recordOne("QunarUBTListener");
                return false;
            }
        } else {
            Metrics.build()
                    .withTag("result", "channelIdNotConfig")
                    .withTag("mktQMQType", "QunarUBT")
                    .recordOne("QunarUBTListener");
        }

        // 3. SourceFrom过滤
        if (!CollectionUtils.isEmpty(jobConfig.getSourceFroms())) {
            String sourceFrom = record.getSourceFrom();
            if (StringUtils.isNotEmpty(sourceFrom)) {
                if (jobConfig.getSourceFroms().contains(sourceFrom)) {
                    Metrics.build()
                            .withTag("result", "sourceFromOver")
                            .withTag("mktQMQType", "QunarUBT")
                            .recordOne("QunarUBTListener");
                } else {
                    Metrics.build()
                            .withTag("result", "sourceFromFilter")
                            .withTag("mktQMQType", "QunarUBT")
                            .recordOne("QunarUBTListener");
                    return false;
                }
            } else {
                Metrics.build()
                        .withTag("result", "sourceFromNullFilter")
                        .withTag("mktQMQType", "QunarUBT")
                        .recordOne("QunarUBTListener");
                return false;
            }
        } else {
            Metrics.build()
                    .withTag("result", "sourceFromNotConfig")
                    .withTag("mktQMQType", "QunarUBT")
                    .recordOne("QunarUBTListener");
        }

        if (!CollectionUtils.isEmpty(jobConfig.getKeyIDs())) {
            String keyId = record.getKeyId();
            if (StringUtils.isNotEmpty(keyId)) {
                if (jobConfig.getKeyIDs().contains(keyId)) {
                    Metrics.build()
                            .withTag("result", "KeyIDFromOver")
                            .withTag("mktQMQType", "QunarUBT")
                            .recordOne("QunarUBTListener");
                } else {
                    Metrics.build()
                            .withTag("result", "KeyIDFilter")
                            .withTag("mktQMQType", "QunarUBT")
                            .recordOne("QunarUBTListener");
                    return false;
                }
            } else {
                Metrics.build()
                        .withTag("result", "KeyIDNullFilter")
                        .withTag("mktQMQType", "QunarUBT")
                        .recordOne("QunarUBTListener");
                return false;
            }
        } else {
            Metrics.build()
                    .withTag("result", "KeyIDmNotConfig")
                    .withTag("mktQMQType", "QunarUBT")
                    .recordOne("QunarUBTListener");
        }

        return true;
    }


}