package com.ctrip.car.market.job.domain.dto;

import java.math.BigDecimal;

public class SeoCommentScoreDto {

    private Long mysql_id;

    private String item_name;

    private String item_id;

    private String item_content;

    private BigDecimal score;

    private String sub_item_score;

    private Integer totalcount;

    public Long getMysql_id() {
        return mysql_id;
    }

    public void setMysql_id(Long mysql_id) {
        this.mysql_id = mysql_id;
    }

    public String getItem_name() {
        return item_name;
    }

    public void setItem_name(String item_name) {
        this.item_name = item_name;
    }

    public String getItem_id() {
        return item_id;
    }

    public void setItem_id(String item_id) {
        this.item_id = item_id;
    }

    public String getItem_content() {
        return item_content;
    }

    public void setItem_content(String item_content) {
        this.item_content = item_content;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public String getSub_item_score() {
        return sub_item_score;
    }

    public void setSub_item_score(String sub_item_score) {
        this.sub_item_score = sub_item_score;
    }

    public Integer getTotalcount() {
        return totalcount;
    }

    public void setTotalcount(Integer totalcount) {
        this.totalcount = totalcount;
    }
}
