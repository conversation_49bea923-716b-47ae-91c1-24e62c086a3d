package com.ctrip.car.market.job.domain.mq.mkt;


import com.ctrip.bdai.basebiz.data.ubt.avro.UBTDecode;
import com.ctrip.car.market.job.domain.config.JobConfig;
import com.ctrip.car.market.job.domain.dto.coupon.QunarUBTDateDTO;
import com.ctrip.car.market.job.domain.service.MktCouponHandleService;
import com.ctrip.car.market.job.domain.utils.JsonUtils;
import com.ctrip.car.market.job.domain.utils.Metrics;
import com.ctrip.framework.ckafka.client.KafkaClientFactory;
import com.ctrip.framework.ckafka.client.env.EnvProviderRegistry;
import com.ctrip.framework.ckafka.codec.deserializer.HermesAvroDeserializer;
import com.ctrip.framework.ckafka.codec.entity.HermesConsumerConfig;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ApplicationContextEvent;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Duration;
import java.util.Collection;
import java.util.Properties;

import static com.ctrip.car.market.job.domain.constant.CommonConstant.QUNAR_KAFKA_UBT_CONSUMER;
import static com.ctrip.car.market.job.domain.constant.CommonConstant.QUNAR_KAFKA_UBT_TOPIC;
/*

@Component
public class QunarUBTListener implements ApplicationListener<ApplicationContextEvent> {
    private static final ILog log = LogManager.getLogger(QunarUBTListener.class);

    @Override
    public void onApplicationEvent(ApplicationContextEvent event) {
        try {
            initKafkaConsumer();
        } catch (Exception e) {
            log.warn("QunarUBTListener", e);
        }
    }

    public void initKafkaConsumer() throws IOException {

        final String offsetResetConfig =
                System.getenv().getOrDefault(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");

        Properties properties = new Properties();
        properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, offsetResetConfig);
        properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getCanonicalName());
        properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, HermesAvroDeserializer.class.getCanonicalName());
        properties.put(HermesConsumerConfig.HERMES_MESSAGE_CLASS_CONFIG, UBTDecode.class.getCanonicalName()); // 此值为你接收的类
// 其他配置按需添加
        String registryAddress = EnvProviderRegistry.getEnvProvider().getEnv().schemaRegistryAddress();
        properties.put("schema.registry.url", registryAddress);
        Consumer<String, UBTDecode> consumer = KafkaClientFactory.newConsumer(QUNAR_KAFKA_UBT_TOPIC, QUNAR_KAFKA_UBT_CONSUMER, properties);
        log.info("QunarUBTListener", "22222");
        while (true) {
            ConsumerRecords<String, UBTDecode> records = consumer.poll(Duration.ofMillis(1000L));
            for (ConsumerRecord<String, UBTDecode> record : records) {
                try {
                    sendToQunar(record.value());
                } catch (Exception e) {
                    log.error("QunarUBTListener", e);
                }
            }
        }
    }


    @Autowired
    MktCouponHandleService mktCouponHandleService;

    @Autowired
    JobConfig jobConfig;

    public void sendToQunar(UBTDecode record) {
        try {
            log.warn("QunarUBTListener2", JsonUtils.toString(record));
            Metrics.build().withTag("result", "sendToQunar").withTag("mktQMQType", "QunarUBT").recordOne("MktQunarIVRListen");

            QunarUBTDateDTO qunarUBTDateDTO = new QunarUBTDateDTO();
            qunarUBTDateDTO.setAppId(record.getAppId().toString());
            if (record.getDataInfo() != null) {
                qunarUBTDateDTO.setDistributionChannelID(record.getDataInfo().get("distributionChannelId")+"");
                qunarUBTDateDTO.setUbtClickName(record.getDataInfo().get("name") + "");
                qunarUBTDateDTO.setUbtClickENName(record.getDataInfo().get("enName") + "");
                qunarUBTDateDTO.setSourceFrom(record.getDataInfo().get("sourceFrom") + "");

                if (!CollectionUtils.isEmpty(jobConfig.getCtripUBTClientEnames())) {
                    // 配置点击名称滤的话，再过滤。不配置，不过滤
                    if (StringUtils.isNotEmpty(record.getDataInfo().get("enName")) && jobConfig.getCtripUBTClientEnames().contains(record.getDataInfo().get("enName").toString())) {

                        Metrics.build().withTag("result", "eNameFilter").withTag("mktQMQType", "QunarUBT").recordOne("MktQunarIVRListen");
                    } else {
                        Metrics.build().withTag("result", "eNameOver").withTag("mktQMQType", "QunarUBT").recordOne("MktQunarIVRListen");
                        return;
                    }
                }
                if (!CollectionUtils.isEmpty(jobConfig.getQappUBTChannelIds())) {

                    // 配置渠道过滤的话，再过滤。不配置，不过滤
                    if (StringUtils.isNotEmpty(record.getDataInfo().get("distributionChannelId")) && jobConfig.getQappUBTChannelIds().contains((record.getDataInfo().get("distributionChannelId").toString()))) {
                        Metrics.build().withTag("result", "channelFilter").withTag("mktQMQType", "QunarUBT").recordOne("MktQunarIVRListen");
                    } else {
                        Metrics.build().withTag("result", "channelOver").withTag("mktQMQType", "QunarUBT").recordOne("MktQunarIVRListen");
                        return;
                    }
                }

                if (!CollectionUtils.isEmpty(jobConfig.getSourceFroms())) {
                    qunarUBTDateDTO.setDistributionChannelID(record.getDataInfo().get("sourceFrom").toString());

                    // 配置渠道过滤的话，再过滤。不配置，不过滤
                    if (StringUtils.isNotEmpty(record.getDataInfo().get("sourceFrom")) && jobConfig.getSourceFroms().contains((record.getDataInfo().get("sourceFrom").toString()))) {
                        Metrics.build().withTag("result", "sourceFromFilter").withTag("mktQMQType", "QunarUBT").recordOne("MktQunarIVRListen");
                    } else {
                        Metrics.build().withTag("result", "sourceFromOver").withTag("mktQMQType", "QunarUBT").recordOne("MktQunarIVRListen");
                        return;
                    }
                }

            }
            qunarUBTDateDTO.setDateType(1);

            qunarUBTDateDTO.setUserID(record.getUid() + "");
            qunarUBTDateDTO.setUserName(record.getLoginName() + "");
            qunarUBTDateDTO.setPageID(record.getPage() + "");
            qunarUBTDateDTO.setKeyId(record.getKeyId() + "");
            log.info("QunarUBTListener", "44444");
            mktCouponHandleService.sendQMQ(qunarUBTDateDTO);
            log.info("QunarUBTListener", "55555");
            Metrics.build().withTag("result", "sendSuc").withTag("mktQMQType", "QunarUBT").recordOne("MktQunarIVRListen");

        } catch (Exception ex) {
            log.warn("UBTDecode", ex);
            Metrics.build().withTag("result", "sendError").withTag("mktQMQType", "QunarUBT").recordOne("MktQunarIVRListen");

        }

    }

}*/
