package com.ctrip.car.market.job.domain.dto.coupon;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QunarUBTDateDTO {

    /**
     * 去哪userID
     */

    private String userID;
    /**
     * 去哪userName , 优惠券无该字段，UBT有该字段
     */
    private String userName;

    /**
     * 优惠券code， 优惠券才会有
     */
    private String couponCode;
    /**
     * 优惠券策略ID，， 优惠券才会有
     */

    private Integer promotionID;
    /**
     * 优惠券状态 ， 0 已经去，未使用， 1 已使用，2 取消使用，   3作废  ，4 取消作废优惠券 优惠券才会有
     */

    private Integer useStatus;

    /**
     *  订单号， 部分优惠券情况才会有 ，
     */
    private String orderID;
    /**
     * 页面ID， UBT数据才会有
     */

    private String pageID;
    /**
     * 渠道号，UBT数据才会有
     */
    private String distributionChannelID;
    /**
     * UBT点击英文名称，UBT数据才会有
     */
    private String ubtClickENName;
    /**
     * UBT点击名称，UBT数据才会有
     */
    private String ubtClickName;
    /**
     * UBT 数据来源，UBT数据才会有
     */
    private String sourceFrom;


    /*
      0 优惠券  1 实时UBT数据
     */
    private  Integer dateType;

    private  String  appId;

    private  String  keyId;

    private  String bu;

}
