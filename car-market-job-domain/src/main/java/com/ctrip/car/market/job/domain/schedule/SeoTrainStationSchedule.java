package com.ctrip.car.market.job.domain.schedule;
import com.ctrip.car.market.job.domain.dto.BiTrainStationInformationDto;
import com.ctrip.car.market.job.domain.enums.SeoHotStatusEnums;
import com.ctrip.car.market.job.domain.proxy.GeoProxy;
import com.ctrip.car.market.job.domain.service.SeoHotDestinationBusiness;
import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.domain.utils.StringFormat;
import com.ctrip.car.market.job.repository.entity.SeoHotDestinatioinfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.daas.controller.DaasApiRequestType;
import com.ctrip.daas.controller.DaasApiResponseType;
import com.ctrip.daas.controller.Head;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.igt.geo.interfaces.dto.PlaceDetailsDTO;
import com.ctrip.sysdev.daas.client.DaasClient;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.ctrip.car.market.job.domain.utils.GlobalVariable.*;

/**
 * Author: dhui zou
 * Date: 2025/8/21
 * Description:生成火车站页面的数据
 */
@Component
public class SeoTrainStationSchedule {

    private final ILog log = LogManager.getLogger(SeoTrainStationSchedule.class);

    private final DaasClient daasClient = DaasClient.getInstance();

    @Resource
    private MarketDBService marketDBService;

    @Resource
    private CityRepository cityRepository;

    @Resource
    private SeoHotDestinationBusiness seoHotDestinationBusiness;

    @Resource
    private GeoProxy geoProxy;

    private final String logTitle = "SeoTrainStationSchedule";

    @QSchedule("car.market.seo.train.station.sync")
    public void task() throws Exception {
        // 获取BI火车站数据 不需要删除数据库已有的数据
        List<BiTrainStationInformationDto> biTrainStationList = getBiTrainStationData();
        if (CollectionUtils.isEmpty(biTrainStationList)) {
            log.warn(logTitle, "no train station data from BI");
            return;
        }
        // 获取数据库中已有的火车站数据 set用于去重
        List<SeoHotDestinatioinfo> dbTrainStationList = marketDBService.queryAllHotPoi()
                .stream()
                .filter(poi -> Objects.equals(poi.getPoiType(), TRAIN_CODE))
                .collect(Collectors.toList());

        Set<String> existingTrainStations = dbTrainStationList.stream()
                .filter(poi -> Objects.equals(poi.getPoiType(), TRAIN_CODE))
                .map(poi -> poi.getPoiCode().toLowerCase() + "_" + poi.getPoiType())
                .collect(Collectors.toSet());

        List<SeoHotDestinatioinfo> newTrainStationList = Lists.newArrayList();

        // 处理BI数据，生成火车站页面
        for (BiTrainStationInformationDto trainStationDto : biTrainStationList) {
            try {
                // 验证BI数据有效性
                if ( StringUtils.isEmpty(trainStationDto.getPickuplocationcode())) {
                    continue;
                }
                String trainStationKey = trainStationDto.getPickuplocationcode() + "_" + TRAIN_CODE;
                // 避免和数据库中数据重复
                if (existingTrainStations.contains(trainStationKey.toLowerCase())) {
                    continue;
                }
                // 构建火车站页面数据
                SeoHotDestinatioinfo trainStationPage = buildTrainStationDestination(trainStationDto);
                if (trainStationPage != null) {
                    newTrainStationList.add(trainStationPage);
                    existingTrainStations.add(trainStationKey.toLowerCase());
                }
            } catch (Exception e) {
                log.warn(logTitle, "process train station failed: " + trainStationDto.getPickuplocationcode());
            }
        }

        // 批量插入新的火车站数据
        if (CollectionUtils.isNotEmpty(newTrainStationList)) {
            seoHotDestinationBusiness.batchInsertIntoDestination(newTrainStationList);
        }
    }

    /**
     *  获取BI火车站数据
     */
    private List<BiTrainStationInformationDto> getBiTrainStationData() throws Exception {
        long offset = 0;
        long rows = 500;
        List<BiTrainStationInformationDto> result = Lists.newArrayList();
        List<BiTrainStationInformationDto> temp = null;

        do {
            DaasApiRequestType requestType = new DaasApiRequestType();
            Map<String, Object> params = new HashMap<>();
            params.put("offset", offset);
            params.put("rows", rows);
            requestType.setHead(new Head("JXD6bj82yTo2mn1jtCo1xA==", 100043032, "getAdmGeoCarTripTrainPoiReportDf"));
            requestType.setParams(JsonUtil.toJSONString(params));

            DaasApiResponseType responseType = daasClient.invoke2(requestType);
            CLogUtil.info("getAdmGeoCarTripTrainPoiReportDf", requestType, responseType);

            if (StringUtils.isNotBlank(responseType.getData())) {
                temp = JsonUtil.parseArray(responseType.getData(), BiTrainStationInformationDto.class);
                if (CollectionUtils.isNotEmpty(temp)) {
                    result.addAll(temp);
                    offset = temp.stream()
                            .map(BiTrainStationInformationDto::getMysql_id)
                            .max(Comparator.comparingLong(o -> o))
                            .get();
                }
            }
        } while (CollectionUtils.isNotEmpty(temp));

        return result;
    }

    /**
     * 构建火车站目的地信息
     */
    private SeoHotDestinatioinfo buildTrainStationDestination(BiTrainStationInformationDto trainStationDto) {
        try {
            SeoHotDestinatioinfo destination = new SeoHotDestinatioinfo();

            destination.setPoiCode(trainStationDto.getPickuplocationcode());
            destination.setPoiType(TRAIN_CODE); // 火车站类型为2
            destination.setStatus(SeoHotStatusEnums.ACTIVE.getCode());

            PlaceDetailsDTO placeDetails = geoProxy.getPoiDetail(trainStationDto.getPickuplocationcode(),"en-US");
            if (placeDetails != null) {
                destination.setPoiName(placeDetails.getName());
                Long cityId = placeDetails.cityId;
                destination.setCityId(cityId.intValue());
                City city = cityRepository.findOne(cityId);
                destination.setCountryId(city.getCountryId().intValue());
                // 构建火车站页面URL：https://tw.trip.com/carhire/station/trainStation-id/
                String trainStationName = StringFormat.removeSpecialCharacters(placeDetails.getName());
                String url = PRE_URL + "station/" +
                        trainStationName.toLowerCase() + "-" + trainStationDto.getPickuplocationcode() +
                        "/";
                destination.setUrl(url);
                log.warn(logTitle, "Cannot find POI ID for code: " + trainStationDto.getPickuplocationcode());
            }else{
                destination.setCityId(0);
                destination.setCountryId(0);
            }
            destination.setOrderNum(0);

            return destination;
        } catch (Exception e) {
            log.warn(logTitle, "build train station destination failed");
            return null;
        }
    }
}