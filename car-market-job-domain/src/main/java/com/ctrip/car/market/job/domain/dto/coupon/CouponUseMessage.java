package com.ctrip.car.market.job.domain.dto.coupon;

import com.ctrip.car.market.job.domain.utils.CustomCalendarDeserializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Calendar;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true) // 关键：忽略未知字段
public class CouponUseMessage {

    // 使用 @JsonProperty 精确匹配 JSON 字段名
    @JsonProperty("CouponCode")
    private String couponCode;

    @JsonProperty("UserID")
    private String userID;

    @JsonProperty("ReceiveProductlineID")
    private int createProductLineID;

    @JsonProperty("UseProductlineIDs")
    private String useProductLineIDs;

    @JsonProperty("PromotionID")
    private Integer promotionID;

    @JsonProperty("SceneType")
    private Integer sceneType;

    @JsonProperty("CreateTime")
    @JsonDeserialize(using = CustomCalendarDeserializer.class) // 自定义日期解析
    private Calendar useDate;

    @JsonProperty("CouponTotalAmount")
    private BigDecimal deductionAmount;

    @JsonProperty("MessageKey")
    private String messageKey;

    // 不需要映射的字段（JSON 中没有的）
    @JsonProperty("CouponID")
    private long couponID;
    @JsonProperty("UseProductLineID")
    private Integer useProductLineID;
    @JsonProperty("UseStatus")
    private Integer useStatus;
    @JsonProperty("OrderID")
    private String orderID;
    private String currencyEnumValue;
    private Integer dbType;
    @JsonProperty("UniqueKey")
    private String UniqueKey;

    @JsonProperty("UserProductlineIDs")
    private String userProductlineIDs;
}