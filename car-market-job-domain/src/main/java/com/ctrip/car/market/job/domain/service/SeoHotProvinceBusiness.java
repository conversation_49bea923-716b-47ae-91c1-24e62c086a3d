package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.repository.dao.SeoHotProvinceinfoDao;
import com.ctrip.car.market.job.repository.entity.SeoHotProvinceinfo;
import com.ctrip.platform.dal.dao.DalHints;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.ctrip.car.market.job.domain.utils.GlobalVariable.SQL_BATCH_SIZE;
import static com.ctrip.car.market.job.domain.utils.GlobalVariable.SQL_QUERY_BATCH_SIZE;

/**
 * Author: dhui zou
 * Date: 2025/8/26
 * Description:省份信息的SEO数据操作
 */
@Component
public class SeoHotProvinceBusiness {

    @Resource
    private SeoHotProvinceinfoDao seoHotProvinceinfoDao;

    /**
     * 获得所有的热门省份列表
     */
    public List<SeoHotProvinceinfo> getSeoHotProvinceinfoList(boolean active) throws SQLException {
        String sql = active ? "select * from seo_hot_provinceinfo where status = 0 order by id limit ?,?"
                : "select * from seo_hot_provinceinfo order by id limit ?,?";
        List<SeoHotProvinceinfo> seoHotProvinceinfoList = new ArrayList<>();
        List<SeoHotProvinceinfo> tempList;
        int no = 0;
        int size = SQL_QUERY_BATCH_SIZE;
        do {
            tempList = seoHotProvinceinfoDao.query(sql, new DalHints(), no * size, size);
            if (CollectionUtils.isNotEmpty(tempList)) {
                seoHotProvinceinfoList.addAll(tempList);
            }
            no++;
        } while (CollectionUtils.isNotEmpty(tempList));
        return seoHotProvinceinfoList;
    }

    /**
     * 分批插入省份表
     */
    public int batchInsertIntoProvinces(List<SeoHotProvinceinfo> seoHotProvinceinfoList) throws SQLException {
        //分批插入
        int offset = 0, size = seoHotProvinceinfoList.size();
        while (offset < size) {
            seoHotProvinceinfoDao.batchInsert(new DalHints(),
                    seoHotProvinceinfoList.stream()
                            .skip(offset)
                            .limit(Math.min(offset + SQL_BATCH_SIZE, size) - offset)
                            .collect(Collectors.toList()));
            offset += SQL_BATCH_SIZE;
        }
        return offset;
    }

    /**
     * 分批更新省份表
     */
    public int batchUpdateProvinces(List<SeoHotProvinceinfo> seoHotProvinceinfoList) throws SQLException {
        // 分批更新
        int offset = 0, size = seoHotProvinceinfoList.size();
        while (offset < size) {
            seoHotProvinceinfoDao.batchUpdate(new DalHints(),
                    seoHotProvinceinfoList.stream()
                            .skip(offset)
                            .limit(Math.min(offset + SQL_BATCH_SIZE, size) - offset)
                            .collect(Collectors.toList()));
            offset += SQL_BATCH_SIZE;
        }
        return offset;
    }

}
