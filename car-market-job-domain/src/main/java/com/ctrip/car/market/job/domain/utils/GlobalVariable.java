package com.ctrip.car.market.job.domain.utils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class GlobalVariable {
    public static String SITE;

    public static String SOURCE;

    public static String CHANNEL;

    public static String PRE_URL;

    public static String LOCALE;

    /**
     * 分批查询的batch size
     */
    public static final int BATCH_SIZE;

    /**
     * db 分批更新和插入的size
     */
    public static final int SQL_BATCH_SIZE;

    /**
     * db 分批查询的size
     */
    public static final int SQL_QUERY_BATCH_SIZE;

    /**
     * bi 拉取热门供应商等信息 token
     */
    public static final String CAR_SEO_RENTAL_TOKEN = "mgFiD0Hj-2675-1726143542902";

    /**
     * bi 拉取热门目的地信息 token
     */
    public static final String CAR_POPULAR_AIRPORT_TOKEN = "xFCyW7VN-2668-1726135588635";

    /**
     * bi 拉取的 api
     */
    public static final String BI_API_NAME = "getEdwOrdCarPopularAirportDf";

    /**
     * 推送 ibu 的 qmq sub
     */
    public static final String SEO_IBU_QMQ_SUBJECT = "ibu.seo.plt.admin.seo.page.row";

    /**
     * 推送 ibu 的 qmq type
     */
    public static final String SEO_IBU_QMQ_TYPE = "carhire_0";

    /**
     * 机场1
     */
    public static final Integer AIRPORT_CODE = 1;

    /**
     * 火车站2
     */
    public static final Integer TRAIN_CODE = 2;

    /**
     * 景点20
     */
    public static final Integer SCENERY_CODE = 20;


    public static final String TURKEY = "Turkey";

    public static final Long TURKEY_ID = 89L;

    public static final Integer TURKEY_ID_INT = 89;

    public static final List<Integer> TW_CITY_IDS;

    static {
        SITE = QConfigUtil.getSeoHotConfigOrDefault("seo.ibu.qmq.site", "EN");
        SOURCE = QConfigUtil.getSeoHotConfigOrDefault("seo.ibu.qmq.source", "google");
        CHANNEL = QConfigUtil.getSeoHotConfigOrDefault("seo.ibu.qmq.channel", "carhire");
        PRE_URL = QConfigUtil.getSeoHotConfigOrDefault("seo.ibu.qmq.pre.url", "https://www.trip.com/carhire/");
        LOCALE = QConfigUtil.getSeoHotConfigOrDefault("seo.ibu.qmq.locale", "en-ID,en-PH,nl-NL,pt-BR,tr-TR,pt-PT,de-DE,fr-FR,th-TH,en-AU,en-SG,es-ES,it-IT,en-CA,en-GB,en-US,en-XX,ja-JP,ko-KR,zh-HK,zh-TW");
        BATCH_SIZE = Integer.parseInt(QConfigUtil.getSeoHotConfigOrDefault("pull.batch.size", "500"));
        SQL_BATCH_SIZE = Integer.parseInt(QConfigUtil.getSeoHotConfigOrDefault("sql.batch.size", "200"));
        SQL_QUERY_BATCH_SIZE = Integer.parseInt(QConfigUtil.getSeoHotConfigOrDefault("sql.query.batch.size", "500"));
        TW_CITY_IDS = Arrays.stream(QConfigUtil.getSeoHotConfigOrDefault("twCityList","617,720,3845,3847,3848,3849,5152,5589,6954,7203,7523,7524,7570,7614,7662,7805,7808,7809,7810,7811,650358,669328").split(",")).map(Integer::valueOf).collect(Collectors.toList());
    }

}

