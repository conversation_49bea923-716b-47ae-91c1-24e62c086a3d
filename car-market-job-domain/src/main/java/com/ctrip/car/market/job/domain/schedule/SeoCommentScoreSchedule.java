package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.dto.SeoCommentScoreDto;
import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.repository.entity.SeoCommentScore;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.daas.controller.DaasApiRequestType;
import com.ctrip.daas.controller.DaasApiResponseType;
import com.ctrip.daas.controller.Head;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.sysdev.daas.client.DaasClient;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class SeoCommentScoreSchedule {

    private final DaasClient daasClient = DaasClient.getInstance();

    private final ILog log = LogManager.getLogger(SeoCommentScoreSchedule.class);

    @Resource
    private MarketDBService marketDBService;

    @QSchedule("car.market.seo.comment.score.sync")
    public void seoCommentScoreSync(Parameter parameter) throws Exception {
        List<SeoCommentScoreDto> apiList = getBICommentScore();
        //过滤无效数据
        apiList = apiList.stream().filter(l -> StringUtils.isNotEmpty(l.getItem_id()) && StringUtils.isNotEmpty(l.getItem_name())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(apiList)) {
            log.warn("seoVendorInformationSync", "no data");
            return;
        }
        List<SeoCommentScore> dbList = marketDBService.queryAllSeoCommentScore();
        Map<String, SeoCommentScoreDto> commentScoreMap = apiList.stream().collect(Collectors.toMap(l -> l.getItem_name().toUpperCase() + "-" + l.getItem_id(), l -> l, (k1, k2) -> k1));
        Map<String, SeoCommentScore> dbMap = dbList.stream().collect(Collectors.toMap(l -> l.getItemType().toUpperCase() + "-" + l.getItemId(), l -> l, (k1, k2) -> k1));
        for (Map.Entry<String, SeoCommentScoreDto> kv : commentScoreMap.entrySet()) {
            try {
                if (dbMap.containsKey(kv.getKey())) {
                    //更新
                    SeoCommentScore value = dbMap.get(kv.getKey());
                    value.setScore(kv.getValue().getScore());
                    value.setTotalCount(Optional.ofNullable(kv.getValue().getTotalcount()).orElse(0));
                    value.setSubItemScore(kv.getValue().getSub_item_score());
                    marketDBService.updateSeoCommentScore(value);
                } else {
                    //新增
                    SeoCommentScore value = new SeoCommentScore();
                    value.setItemType(kv.getValue().getItem_name());
                    value.setItemId(Long.valueOf(kv.getValue().getItem_id()));
                    value.setSubItemScore(kv.getValue().getSub_item_score());
                    value.setScore(kv.getValue().getScore());
                    value.setTotalCount(Optional.ofNullable(kv.getValue().getTotalcount()).orElse(0));
                    value.setSubItemScore(kv.getValue().getSub_item_score());
                    value.setStatus(0);
                    marketDBService.insertSeoCommentScore(value);
                }
            } catch (Exception e) {
                log.warn("seoCommentScoreSync", e);
            }
        }
    }


    private List<SeoCommentScoreDto> getBICommentScore() throws Exception {
        long offset = 0;
        long row = 500;
        List<SeoCommentScoreDto> result = Lists.newArrayList();
        List<SeoCommentScoreDto> temp = null;
        do {
            DaasApiRequestType requestType = new DaasApiRequestType();
            Map<String, Object> params = new HashMap<>();
            params.put("offset", offset);
            params.put("rows", row);
            requestType.setHead(new Head("JXD6bj82yTo2mn1jtCo1xA==", 100043032, "getAdmSevCarCommentscoreRecalculateOsdAreaDf"));
            requestType.setParams(JsonUtil.toJSONString(params));
            DaasApiResponseType responseType = daasClient.invoke2(requestType);
            CLogUtil.info("getAdmSevCarCommentscoreRecalculateOsdAreaDf", requestType, responseType);
            if (StringUtils.isNotBlank(responseType.getData())) {
                temp = JsonUtil.parseArray(responseType.getData(), SeoCommentScoreDto.class);
                if (CollectionUtils.isNotEmpty(temp)) {
                    result.addAll(temp);
                    offset = temp.stream().map(SeoCommentScoreDto::getMysql_id).max(Comparator.comparingLong(o -> o)).get();
                }
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }
}
