package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.config.SeoStationSceneryConfig;
import com.ctrip.car.market.job.domain.config.SeoPoiConfig;
import com.ctrip.car.market.job.domain.dto.PoiA;
import com.ctrip.car.market.job.domain.enums.SeoHotStatusEnums;
import com.ctrip.car.market.job.domain.proxy.GlobalPoiNameJavaProxy;
import com.ctrip.car.market.job.domain.service.SeoHotDestinationBusiness;
import com.ctrip.car.market.job.domain.utils.StringFormat;
import com.ctrip.car.market.job.repository.entity.SeoHotDestinatioinfo;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.repository.CountryRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.geo.domain.value.Country;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

import static com.ctrip.car.market.job.domain.utils.GlobalVariable.*;
import static com.ctrip.car.market.job.domain.utils.GlobalVariable.PRE_URL;
/**
 * Author: dhui zou
 * Date: 2025/8/21
 * Description:SEO生成景点页面
 */
@Component
public class SeoScenerySchedule {

    private final ILog log = LogManager.getLogger(SeoScenerySchedule.class);

    @Resource
    private CountryRepository countryRepository;

    @Resource
    private CityRepository cityRepository;

    @Resource
    private SeoStationSceneryConfig seoStationSceneryConfig;

    @Resource
    private GlobalPoiNameJavaProxy  globalPoiNameJavaProxy;

    @Resource
    private SeoHotDestinationBusiness seoHotDestinationBusiness;

    private final String logTitle = "SeoScenerySchedule";

    @QSchedule("car.market.seo.pull.hot.scenery.sync")
    public void task() throws Exception {
        // 获取运营配置poi
        List<SeoPoiConfig> PoiConfigList = seoStationSceneryConfig.getseoPoiConfigList();
        // 获取数据库中现有的景点记录（poiType = 20）
        List<SeoHotDestinatioinfo> existingSceneryList = getSeoHotSceneryList();
        // 将现有记录转换为Map，方便查找（使用 cityId + poiId 作为唯一键）
        Map<String, SeoHotDestinatioinfo> dbSceneryMap = existingSceneryList.stream()
                .collect(Collectors.toMap(
                        scenery -> scenery.getCityId() + "_" + scenery.getPoiId(),
                        scenery -> scenery
                ));
        // 处理新增和更新
        List<SeoHotDestinatioinfo> updateList = new ArrayList<>();
        List<SeoHotDestinatioinfo> insertList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(PoiConfigList)) {
            for (SeoPoiConfig item : PoiConfigList) {
                int cityId = item.getCityId();
                PoiA poiA = item.getPoiA();
                if (poiA != null && Objects.equals(poiA.getType(), "scenery")) { // 使用poi A，类型为景点 scenery
                    City city = cityRepository.findOne((long) cityId);
                    if (city == null) {
                        log.warn(logTitle, "city is null:" + cityId);
                        continue;
                    }
                    Country country = countryRepository.findOne(city.getCountryId());
                    if (country == null) {
                        log.warn(logTitle, "country is null:" + city.getCountryId());
                        continue;
                    }
                    // 生成景点页面数据 如果poiA找不到对应景点名 跳过
                    SeoHotDestinatioinfo sceneryPage = buildSceneryDestination(country, city, poiA);  //poiADto内部提供PoiId
                    if(sceneryPage == null) continue;
                    String uniqueKey = sceneryPage.getCityId() + "_" + sceneryPage.getPoiId();
                    SeoHotDestinatioinfo existingScenery = dbSceneryMap.get(uniqueKey);
                    if (existingScenery != null) {
                        // 在库中存在 更新记录
                        updateExistingScenery(existingScenery, sceneryPage);
                        updateList.add(existingScenery);
                        log.info(logTitle, "Updated scenery: " + sceneryPage.getPoiName() + " (ID: " + sceneryPage.getPoiId() + ")");
                    } else {
                        // 不存在 新增记录
                        insertList.add(sceneryPage);
                        dbSceneryMap.put(uniqueKey,sceneryPage);
                        log.info(logTitle, "Added new scenery: " + sceneryPage.getPoiName() + " (ID: " + sceneryPage.getPoiId() + ")");
                    }
                }
            }
            // 处理需要设置为失效状态的记录 existingSceneryList存在 PoiConfigList不存在
            handleInactiveScenery(PoiConfigList, existingSceneryList, updateList);
            // 批量执行数据库操作
            executeBatchOperations(updateList, insertList);
        }
    }

    /**
     * 更新现有的景点记录
     */
    private void updateExistingScenery(SeoHotDestinatioinfo existing, SeoHotDestinatioinfo newData) {
        // 更新可能变化的字段
        existing.setPoiName(newData.getPoiName());
        existing.setUrl(newData.getUrl());
        existing.setStatus(SeoHotStatusEnums.ACTIVE.getCode());
        existing.setCountryId(newData.getCountryId());
        existing.setPoiCode(newData.getPoiCode());
    }

    /**
     * 处理需要设置为失效状态的记录
     */
    private void handleInactiveScenery(List<SeoPoiConfig> poiConfigList, List<SeoHotDestinatioinfo> existingSceneryList, List<SeoHotDestinatioinfo> updateList) {
        if (CollectionUtils.isEmpty(poiConfigList)) {
            return;
        }

        // 构建poi配置中的 cityId + poiId 组合集合
        Set<String> activeSceneryKeys = poiConfigList.stream()
                .filter(config -> config.getPoiA() != null && Objects.equals(config.getPoiA().getType(), "scenery"))
                .map(config -> config.getCityId() + "_" + config.getPoiA().getPoiId())
                .collect(Collectors.toSet());

        // 检查现有记录，如果不在配置中 而且之前是有效的 则设置为失效
        for (SeoHotDestinatioinfo existing : existingSceneryList) {
            String existingKey = existing.getCityId() + "_" + existing.getPoiId();
            if (!activeSceneryKeys.contains(existingKey) && existing.getStatus() == SeoHotStatusEnums.ACTIVE.getCode()) {
                // 设置为失效状态
                existing.setStatus(SeoHotStatusEnums.INACTIVE.getCode());
                updateList.add(existing);
                log.info(logTitle, "Set scenery to inactive: " + existing.getPoiName() + " (ID: " + existing.getPoiId() + ")");
            }
        }
    }

    /**
     * 批量执行数据库操作
     */
    private void executeBatchOperations(List<SeoHotDestinatioinfo> updateList,
                                        List<SeoHotDestinatioinfo> insertList) throws SQLException {
        // 批量更新
        if (CollectionUtils.isNotEmpty(updateList)) {
            seoHotDestinationBusiness.batchUpdateDestination(updateList);
            log.info(logTitle, "Batch updated " + updateList.size() + " scenery records");
        }
        // 批量插入
        if (CollectionUtils.isNotEmpty(insertList)) {
            seoHotDestinationBusiness.batchInsertIntoDestination(insertList);
            log.info(logTitle, "Batch inserted " + insertList.size() + " scenery records");
        }
    }

    /**
     * 获取数据库中现有的景点记录（poiType = 20）
     */
    private List<SeoHotDestinatioinfo> getSeoHotSceneryList() throws SQLException {
        List<SeoHotDestinatioinfo> allDestinations = seoHotDestinationBusiness.getSeoScenerySpotList(true);
        return allDestinations.stream()
                .filter(dest -> SCENERY_CODE.equals(dest.getPoiType()))
                .collect(Collectors.toList());
    }

    public SeoHotDestinatioinfo buildSceneryDestination(Country country, City city, PoiA poiA) {
        // 获取景点名
        String poiName = getPoiNameById(poiA.getPoiId());
        if (StringUtils.isEmpty(poiName)) {
            return null; // 如果找不到景点名 返回null 外层循环直接跳出
        }
        String sceneryName = StringFormat.removeSpecialCharacters(poiName).toLowerCase();

        SeoHotDestinatioinfo seoHotDestinatioinfo = new SeoHotDestinatioinfo();
        seoHotDestinatioinfo.setCityId(city.getId().intValue());
        seoHotDestinatioinfo.setStatus(SeoHotStatusEnums.ACTIVE.getCode());
        // 构建景点页面URL：https://tw.trip.com/carhire/poi/poiName-id/
        String url = PRE_URL + "poi/" +
                sceneryName.toLowerCase() + "-" + poiA.getPoiId() + "/";

        seoHotDestinatioinfo.setUrl(url);
        seoHotDestinatioinfo.setPoiType(SCENERY_CODE); // 景点类型 20
        seoHotDestinatioinfo.setPoiId(poiA.getPoiId());
        seoHotDestinatioinfo.setOrderNum(0);
        seoHotDestinatioinfo.setPoiName(poiName);
        seoHotDestinatioinfo.setCountryId(country.getId().intValue());
        seoHotDestinatioinfo.setPoiCode(String.valueOf(poiA.getPoiId()));
        return seoHotDestinatioinfo;
    }

    // 添加获取POI名称的方法
    private String getPoiNameById(Long poiId) {
        try {
            Map<Long, String> PoiName = globalPoiNameJavaProxy.getPoiNamesByIds(Collections.singletonList(poiId));
            return PoiName.get(poiId);
        } catch (Exception e) {
            log.warn(logTitle, "Failed to get poi name by id: " + poiId);
            return null;
        }
    }

}

