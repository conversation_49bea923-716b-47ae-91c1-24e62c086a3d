package com.ctrip.car.market.job.domain.service;


import com.ctrip.car.market.job.common.consts.CacheName;
import com.ctrip.car.market.job.domain.config.JobConfig;
import com.ctrip.car.market.job.domain.dto.coupon.CouponUseMessage;
import com.ctrip.car.market.job.domain.dto.coupon.QunarUBTDateDTO;
import com.ctrip.car.market.job.domain.proxy.PromotionProxy;
import com.ctrip.car.market.job.domain.utils.Metrics;
import com.ctrip.car.market.job.domain.utils.RedisUtil;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.soa.platform.account.promocodeservice.message.v1.GetPromotionStrategyResponseType;
import credis.java.client.util.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageProducer;
import qunar.tc.qmq.MessageSendStateListener;

import javax.annotation.Resource;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ctrip.car.market.job.domain.constant.CommonConstant.QUNAR_UBT_USER_INFO_TOPIC;


@Service
public class MktCouponHandleService {

    @Resource
    private MessageProducer messageProducer;

    @Autowired
    private PromotionProxy promotionProxy;

    @Autowired
    JobConfig jobConfig;
    private static final ILog log = LogManager.getLogger(MktCouponHandleService.class);
    String KeyFormat = "mkt_coupon_%s_";


    public boolean sendQunarCouponQmq(CouponUseMessage mktCouponQMQDTo, String mktQMQType) {
        if (mktCouponQMQDTo == null) return false;
        if (!JudgeLineIsCar(mktCouponQMQDTo, mktQMQType)) {
            return false;
        }
        String key = String.format(KeyFormat, mktCouponQMQDTo.getUniqueKey());
        if (StringUtils.isNotEmpty(mktCouponQMQDTo.getUniqueKey())) {
            // 有值的话 才是发放，没有值的话，就不是发放，
            if (StringUtils.isNotEmpty(RedisUtil.getCache(key))) {
                // 已经有缓存的话，就不再推送，没有换粗能的话，就推送
                Metrics.build().withTag("result", "UniqueKeyFilter").withTag("mktQMQType", mktQMQType).recordOne(mktQMQType);
                return false;
            } else {
                // 没有的话，就先设置缓存去
                RedisUtil.set(key, key, jobConfig.getPromotionCategoryformCacheExpired());

            }
        }
        mktCouponQMQDTo.setUserID(convertQunarUserId(mktCouponQMQDTo.getUserID()));
        QunarUBTDateDTO qunarUBTDateDTO = convertTOQunarUBTDateDTO(mktCouponQMQDTo);
        sendQMQ(qunarUBTDateDTO);
        return true;
    }

    public void sendQMQ(QunarUBTDateDTO qunarUBTDateDTO) {

        Message message = messageProducer.generateMessage(QUNAR_UBT_USER_INFO_TOPIC);
        message.setProperty("dateType", qunarUBTDateDTO.getDateType());
       // message.setProperty("data", JsonUtil.toJson(qunarUBTDateDTO));
        message.setProperty("couponCode", qunarUBTDateDTO.getCouponCode());
        message.setProperty("promotionID", qunarUBTDateDTO.getPromotionID());
        message.setProperty("userID", qunarUBTDateDTO.getUserID());
        message.setProperty("userName", qunarUBTDateDTO.getUserName());
        message.setProperty("useStatus", qunarUBTDateDTO.getUseStatus());
        message.setProperty("orderID", qunarUBTDateDTO.getOrderID());
        message.setProperty("pageID", qunarUBTDateDTO.getPageID());
        message.setProperty("distributionChannelID", qunarUBTDateDTO.getDistributionChannelID());
        message.setProperty("ubtClickENName", qunarUBTDateDTO.getUbtClickENName());
        message.setProperty("ubtClickName", qunarUBTDateDTO.getUbtClickName());
        message.setProperty("sourceFrom", qunarUBTDateDTO.getSourceFrom());
        message.setProperty("appId", qunarUBTDateDTO.getAppId());
        message.setProperty("keyId", qunarUBTDateDTO.getKeyId());

        messageProducer.sendMessage(message, new MessageSendStateListener() {
            @Override
            public void onSuccess(Message message) {
                log.info("MktCouponHandleService.succ", JsonUtil.toJson(qunarUBTDateDTO) + "message producer success");
            }

            @Override
            public void onFailed(Message message) {
                log.error("MktCouponHandleService.fail", JsonUtil.toJson(qunarUBTDateDTO) + "message producer failed");
            }
        });
    }


    public boolean JudgeLineIsCar(CouponUseMessage mktCouponQMQDTo, String mktQMQType) {
        log.warn("JudgeLineIsCa6", JsonUtil.toJson(mktCouponQMQDTo));

        if (StringUtils.isEmpty(mktCouponQMQDTo.getUseProductLineIDs())) {
            log.warn("JudgeLineIsCa7", JsonUtil.toJson(mktCouponQMQDTo));

            mktCouponQMQDTo.setUseProductLineIDs(mktCouponQMQDTo.getUserProductlineIDs());
        }
        String userLins = mktCouponQMQDTo.getUseProductLineIDs();
        if (StringUtils.isEmpty(userLins) && mktCouponQMQDTo.getUseProductLineID() == null) {
            Metrics.build().withTag("result", "false").withTag("mktQMQType", mktQMQType).recordOne("JudgeLineIsCar");
            log.warn("JudgeLineIsCar", JsonUtil.toJson(mktCouponQMQDTo));
            return false;
        }
        if (mktCouponQMQDTo.getUseProductLineID() != null && mktCouponQMQDTo.getUseProductLineID() > 0 && jobConfig.getQunarPromotionUseLines().contains(mktCouponQMQDTo.getUseProductLineID())) {
            Metrics.build().withTag("result", "true").withTag("mktQMQType", mktQMQType).recordOne("JudgeLineIsCar");
            log.warn("JudgeLineIsCa2", JsonUtil.toJson(mktCouponQMQDTo));
            return JudgeLineIsQunarCar(mktCouponQMQDTo.getPromotionID(), mktQMQType);

        }
        if (StringUtils.isNotEmpty(userLins) && jobConfig.getQunarPromotionUseLines().stream().anyMatch(x -> Stream.of(userLins.split(",")).map(Integer::parseInt).collect(Collectors.toList()).contains(x))) {
            Metrics.build().withTag("result", "true").withTag("mktQMQType", mktQMQType).recordOne("JudgeLineIsCar");
            log.warn("JudgeLineIsCa4", JsonUtil.toJson(mktCouponQMQDTo));

            return JudgeLineIsQunarCar(mktCouponQMQDTo.getPromotionID(), mktQMQType);
        }
        log.warn("JudgeLineIsCa3", JsonUtil.toJson(mktCouponQMQDTo));

        Metrics.build().withTag("result", "false").withTag("mktQMQType", mktQMQType).recordOne("JudgeLineIsCar");

        return false;
    }

    public boolean JudgeLineIsQunarCar(Integer promotionId, String mktQMQType) {

        String redisKey = String.format(CacheName.QunarPromotionIDUserStation, promotionId);
        String redisResult = RedisUtil.getCache(redisKey);
        if (StringUtils.isNotEmpty(redisResult)) {
            Metrics.build().withTag("result", redisResult).withTag("mktQMQType", mktQMQType).recordOne("JudgeLineIsQunarCar");
            log.warn("JudgeLineIsQunarCar", JsonUtil.toJson(promotionId));

            return Boolean.parseBoolean(redisResult);
        }
        GetPromotionStrategyResponseType getPromotionStrategyResponseType = promotionProxy.getPromotionStrategy(promotionId);
        if (getPromotionStrategyResponseType != null && getPromotionStrategyResponseType.getPromotionStrategy() != null) {
            boolean result = getPromotionStrategyResponseType.getPromotionStrategy().getUseStation().contains("100") ||
                    getPromotionStrategyResponseType.getPromotionStrategy().getUseStation().contains("110") ||
                    getPromotionStrategyResponseType.getPromotionStrategy().getUseStation().contains("120");
            log.warn("JudgeLineIsQunarCar2", JsonUtil.toJson(promotionId));

            RedisUtil.set(redisKey, result + "", jobConfig.getPromotionCategoryformCacheExpired());
            Metrics.build().withTag("result", result + "").withTag("mktQMQType", mktQMQType).recordOne("JudgeLineIsQunarCar");
            return result;

        }
        Metrics.build().withTag("result", "false").withTag("mktQMQType", mktQMQType).recordOne("JudgeLineIsQunarCar");

        return false;
    }

    public String convertQunarUserId(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return "";
        }
        return userId.substring(2);
    }

    public QunarUBTDateDTO convertTOQunarUBTDateDTO(CouponUseMessage couponUseMessage) {

        return QunarUBTDateDTO.builder().dateType(0).couponCode(couponUseMessage.getCouponCode()).orderID(couponUseMessage.getOrderID()).userID(couponUseMessage.getUserID())
                .promotionID(couponUseMessage.getPromotionID()).useStatus(couponUseMessage.getUseStatus()).build();


    }

}
