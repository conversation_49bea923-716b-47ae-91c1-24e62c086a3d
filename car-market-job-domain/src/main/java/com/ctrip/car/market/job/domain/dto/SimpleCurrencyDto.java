package com.ctrip.car.market.job.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimpleCurrencyDto {
    /**
     * 主键
     */
    public Long id;

    /**
     * 名称
     */
    public String name;

    /**
     * 价值
     */
    public BigDecimal worth;

    /**
     * 货币数量
     */
    public Integer number;

    /**
     * 类型{1:integrated,积分;2:rights,权益包;3:coupon,优惠券;4:lego_times,乐高大转盘抽奖次数;5:external_provider,外部供应商;6:non_award,无奖励发放;7:return_cash,返现;8:train_recharge,火车票加速包;9:rights_coupon,权益包-第三方卡券;100:qmq,qmq;10:red_packet_cover,红包封面}
     */
    public Integer type;

    /**
     * 配置字段
     */
    public String configurationFields;

    /**
     * 实际发放流水(调用具体奖励返回的流水) 注意判空,有些奖励没有流水
     */
    public List<SimpleAwardTransactionDto> transactionDtoList;

}
