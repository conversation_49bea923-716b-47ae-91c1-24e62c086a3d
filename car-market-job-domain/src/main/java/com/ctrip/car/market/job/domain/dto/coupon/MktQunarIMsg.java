package com.ctrip.car.market.job.domain.dto.coupon;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true) // 关键：忽略未知字段
public class MktQunarIMsg {


    private String templateType;
    private String businessCode;
    private String taskGroupId;
    private String customContent;
    private String success;

    private String taskGroupScene;
    private String customEventID;
    private String taskId;
    private String result;

}
