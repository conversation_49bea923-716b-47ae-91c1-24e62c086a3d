package com.ctrip.car.market.job.domain.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class MapConverter {
    // 双向转换工具
    public static Map<String, String> toStringMap(Map<CharSequence, CharSequence> input) {
        if (input == null) return null;
        Map<String, String> result = new HashMap<>(input.size());
        input.forEach((k, v) -> result.put(k + "", v + ""));
        return result;
    }

    public static Map<CharSequence, CharSequence> toCharSeqMap(Map<String, String> input) {
        if (input == null) return null;
        return new HashMap<>(input); // 自动向上转型
    }

    // 线程安全版本
    public static Map<String, String> concurrentConvert(
            Map<CharSequence, CharSequence> input) {
        ConcurrentHashMap<String, String> result = new ConcurrentHashMap<>();
        input.forEach((k, v) -> result.put(k + "", v + ""));
        return result;
    }

    public static  Map<String, String> convertToStringMap(Map<CharSequence, CharSequence> input) {
        if (null == input) {
            return null;
        }
        HashMap<String, String> result = new HashMap<>(input.size());
        for (CharSequence key : input.keySet()) {
            CharSequence k_value = input.get(key);
            if(k_value!=null) {
                String s_key = key.toString();
                String s_value = k_value.toString();
                result.put(s_key, s_value);
            }
        }
        return result;
    }
}