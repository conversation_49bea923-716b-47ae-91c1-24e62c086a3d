package com.ctrip.car.market.job.domain.utils;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @Description:
 * @create 2025/7/16 21:11
 */
public final class StringFormat {
    private StringFormat() {}

    public static String format(String pattern, Object... arguments) {
        for (int i = 0; i < arguments.length; i++) {
            if(arguments[i] == null) {
                arguments[i] = "";
            }
            else {
                arguments[i] = arguments[i].toString();
            }

        }
        pattern= pattern.replace("'","''");
        return MessageFormat.format(pattern, arguments);
    }

    /**
     * 除数字字符串外，其他字符全部替换成-
     * @param input 输入字符串
     * @return 删除特殊字符后的字符串
     */
    public static String removeSpecialCharacters(String input) {
        if (input == null) {
            return null;
        }
        try {
            return input.replaceAll("[^a-zA-Z0-9]", "-");
        } catch (Exception e) {
            return input.replace(" ","-");
        }
    }
}