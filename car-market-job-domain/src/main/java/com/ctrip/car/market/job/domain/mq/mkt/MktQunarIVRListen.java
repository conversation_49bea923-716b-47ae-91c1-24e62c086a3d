package com.ctrip.car.market.job.domain.mq.mkt;

import com.ctrip.car.market.job.domain.config.JobConfig;
import com.ctrip.car.market.job.domain.dto.coupon.MktQunarIMsg;
import com.ctrip.car.market.job.domain.service.MktCouponHandleService;
import com.ctrip.car.market.job.domain.utils.Metrics;
import com.ctrip.car.market.job.repository.dao.ivr.IvrMsgSendResultDao;
import com.ctrip.car.market.job.repository.entity.carbidb.IvrMsgSendResult;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.platform.dal.dao.DalHints;
import com.google.common.base.Strings;
import credis.java.client.util.JsonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Map;


@Component
public class MktQunarIVRListen {
    @Autowired
    MktCouponHandleService mktCouponHandleService;

    @Autowired
    IvrMsgSendResultDao ivrMsgSendResultDao;


    @Autowired
    JobConfig jobConfig;

    private static final ILog log = LogManager.getLogger(MktQunarIVRListen.class);

    //推送结果回调，业务线可接⼊
    @QmqConsumer(prefix = "union.task.custom.callback_custommq", consumerGroup = "car.ivr.100043032.qmq.consumer")
    public void changeMessage(Message message) {
        Metrics.build().withTag("result", "onMsg").withTag("mktQMQType", "callback_custommq").recordOne("MktQunarIVRListen");

        if (!jobConfig.getOpenSaveQunarMsg() || CollectionUtils.isEmpty(jobConfig.getQunarMsgTaskGroupSceces())) {
            Metrics.build().withTag("result", "noOpen").withTag("mktQMQType", "callback_custommq").recordOne("MktQunarIVRListen");
            log.warn("MktQunarIVRListen", "noOpen");
            return;
        }
        String data = message.getStringProperty("data");

        if (Strings.isNullOrEmpty(data)) {
            Metrics.build().withTag("result", "noData").withTag("mktQMQType", "callback_custommq").recordOne("MktQunarIVRListen");
            log.warn("MktQunarIVRListen", "noData");
            return;
        }
        MktQunarIMsg msg = JsonUtil.fromJson(data, MktQunarIMsg.class);
        log.info("MktQunarIVRListen", JsonUtil.toJson(msg));

        try {
            IvrMsgSendResult msgSendResult = new IvrMsgSendResult();
            msgSendResult.setCustomEventID(msg.getCustomEventID());
            msgSendResult.setTaskGroupScene(msg.getTaskGroupScene());
            msgSendResult.setTemplateType(msg.getTemplateType());
            msgSendResult.setTaskGroupId(msg.getTaskGroupId());
            msgSendResult.setTaskId(msg.getTaskId());
            msgSendResult.setSuccess(msg.getSuccess());
            msgSendResult.setResult(msg.getResult());
            if (jobConfig.getQunarMsgTaskGroupSceces().contains(msgSendResult.getTaskGroupScene())) {
                ivrMsgSendResultDao.insert(new DalHints(), msgSendResult);
                Metrics.build().withTag("result", "insertSuc").withTag("mktQMQType", "callback_custommq").recordOne("MktQunarIVRListen");
            } else {
                Metrics.build().withTag("result", "taskGroupSceneFilter").withTag("mktQMQType", "callback_custommq").recordOne("MktQunarIVRListen");
            }

        } catch (Exception e) {
            log.warn("MktQunarIVRListen", e);
            Metrics.build().withTag("result", "exception").withTag("mktQMQType", "callback_custommq").recordOne("MktQunarIVRListen");

        }
    }

}