package com.ctrip.car.market.job.domain.beanConfig;

import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.concurrent.TimeUnit;
import com.ctrip.arch.distlock.DistributedLockService;
import com.ctrip.arch.distlock.redis.RedisDistributedLockService;

import static com.ctrip.car.market.job.domain.constant.CommonConstant.QUNAR_KAFKA_UBT_CONSUMER;
import static com.ctrip.car.market.job.domain.constant.CommonConstant.QUNAR_KAFKA_UBT_TOPIC;

@Configuration
public class BeanConfig {
//    @Bean
//    public DistributedLockService distributedLockService(){
//        return new RedisDistributedLockService("com.ctrip.car.market.job.domain.ivr.lock", 60, TimeUnit.SECONDS);
//    }


}