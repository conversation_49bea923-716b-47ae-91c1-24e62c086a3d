package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.config.SeoHotVendorConfig;
import com.ctrip.car.market.job.domain.config.SeoVendorCityPageConfig;
import com.ctrip.car.market.job.domain.dto.BiVendorCommentScoreDto;
import com.ctrip.car.market.job.domain.dto.BiVendorInformationDto;
import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.repository.entity.SeoHotVendorInformation;
import com.ctrip.car.market.job.repository.entity.SeoVendorCommentScore;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.daas.controller.DaasApiRequestType;
import com.ctrip.daas.controller.DaasApiResponseType;
import com.ctrip.daas.controller.Head;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.sysdev.daas.client.DaasClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class SeoVendorSchedule {

    private final ILog log = LogManager.getLogger(SeoVendorSchedule.class);

    private final DaasClient daasClient = DaasClient.getInstance();

    @Resource
    private MarketDBService marketDBService;

    @Resource
    private SeoHotVendorConfig seoHotVendorConfig;

    @QSchedule("car.market.seo.vendor.information.sync")
    public void seoVendorInformationSync(Parameter parameter) throws Exception {
        //获取全量供应商热门数据
        List<BiVendorInformationDto> apiList = getBiVendorInformationData();
        //过滤无效数据
        apiList = apiList.stream().filter(l -> StringUtils.isNotEmpty(l.getPickupcityid())
                && StringUtils.isNotEmpty(l.getVendorcode())
                && StringUtils.isNotEmpty(l.getPickuplocationcode())
                && NumberUtils.isNumber(l.getPickuplocationtype())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(apiList)) {
            log.warn("seoVendorInformationSync", "no data");
            return;
        }

        List<BiVendorInformationDto> list = Lists.newArrayList();
        for (SeoVendorCityPageConfig config : seoHotVendorConfig.getSeoVendorCityPageConfigList()) {
            Set<String> citySet = CollectionUtils.isNotEmpty(config.getCityIdList()) ? config.getCityIdList().stream().map(l -> l.getCityId().toString()).collect(Collectors.toSet()) : Sets.newHashSet();
            List<BiVendorInformationDto> cityList = apiList.stream().filter(l -> StringUtils.equalsIgnoreCase(l.getVendorcode(), config.getVendorCode()) && citySet.contains(l.getPickupcityid())).collect(Collectors.toList());
            List<BiVendorInformationDto> noCityList = apiList.stream().filter(l -> StringUtils.equalsIgnoreCase(l.getVendorcode(), config.getVendorCode()) && !citySet.contains(l.getPickupcityid())).collect(Collectors.toList());
            list.addAll(cityList);
            if (cityList.size() < 3) {
                list.addAll(noCityList.stream().limit(3 - cityList.size()).collect(Collectors.toList()));
            }
        }

        List<SeoHotVendorInformation> dbList = marketDBService.queryALlVendorInformation();
        Map<String, List<BiVendorInformationDto>> vendorCityMap = list.stream().collect(Collectors.groupingBy(l -> l.getVendorcode().toUpperCase().trim() + "_" + l.getPickupcityid()));
        Map<String, SeoHotVendorInformation> dbMap = dbList.stream().collect(Collectors.toMap(l -> l.getVendorCode().toUpperCase() + "_" + l.getCityId(), l -> l, (k1, k2) -> k1));
        for (Map.Entry<String, List<BiVendorInformationDto>> kv : vendorCityMap.entrySet()) {
            try {
                if (dbMap.containsKey(kv.getKey())) {
                    //更新
                    SeoHotVendorInformation value = vendorInformationConvert(kv.getValue().stream().max(Comparator.comparing(BiVendorInformationDto::getRequestid_cnt)).get());
                    if (value == null) {
                        continue;
                    }
                    SeoHotVendorInformation dbValue = dbMap.get(kv.getKey());
                    value.setId(dbValue.getId());
                    value.setDatachangeCreatetime(dbValue.getDatachangeCreatetime());
                    value.setDatachangeLasttime(dbValue.getDatachangeLasttime());
                    marketDBService.updateVendorInformation(value);
                } else {
                    //新增
                    SeoHotVendorInformation value = vendorInformationConvert(kv.getValue().stream().max(Comparator.comparing(BiVendorInformationDto::getRequestid_cnt)).get());
                    if (value == null) {
                        continue;
                    }
                    marketDBService.insertVendorInformation(value);
                }
            } catch (Exception e) {
                log.warn("seoVendorInformationSync", e);
            }
        }

        //接口没有返回的数据置无效
        List<SeoHotVendorInformation> deleteList = Lists.newArrayList();
        for (SeoHotVendorInformation dbValue : dbList) {
            String key = dbValue.getVendorCode().toUpperCase() + "_" + dbValue.getCityId();
            if (!vendorCityMap.containsKey(key)) {
                deleteList.add(dbValue);
            }
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            for (SeoHotVendorInformation l : deleteList) {
                l.setStatus(1);
                marketDBService.updateVendorInformation(l);
            }
        }
    }

    @QSchedule("car.market.seo.vendor.comment.score.sync")
    public void seoVendorCommentScoreSync(Parameter parameter) throws Exception {
        List<BiVendorCommentScoreDto> apiList = getBiVendorCommentScoreData();
        Set<String> vendorSet = seoHotVendorConfig.getSeoVendorCityPageConfigList().stream().map(l -> l.getVendorCode().toUpperCase().trim()).collect(Collectors.toSet());
        //过滤无效数据
        apiList = apiList.stream().filter(l -> StringUtils.isNotEmpty(l.getVendorcode()) && vendorSet.contains(l.getVendorcode().toUpperCase().trim())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(apiList)) {
            log.warn("seoVendorInformationSync", "no data");
            return;
        }
        List<SeoVendorCommentScore> dbList = marketDBService.queryAllVendorScore();
        Map<String, BiVendorCommentScoreDto> vendorScoreMap = apiList.stream().collect(Collectors.toMap(l -> l.getVendorcode().toUpperCase().trim(), l -> l, (k1, k2) -> k1));
        Map<String, SeoVendorCommentScore> dbMap = dbList.stream().collect(Collectors.toMap(l -> l.getVendorCode().toUpperCase().trim(), l -> l, (k1, k2) -> k1));
        for (Map.Entry<String, BiVendorCommentScoreDto> kv : vendorScoreMap.entrySet()) {
            try {
                if (dbMap.containsKey(kv.getKey())) {
                    //更新
                    SeoVendorCommentScore value = dbMap.get(kv.getKey());
                    value.setSocre(new BigDecimal(Optional.ofNullable(kv.getValue().getScore()).orElse(0D).toString()));
                    value.setTotalCount(Optional.ofNullable(kv.getValue().getTotalcount()).orElse(0));
                    value.setSubItemScore(kv.getValue().getSub_item_score());
                    marketDBService.updateVendorCommentScore(value);
                } else {
                    //新增
                    SeoVendorCommentScore value = new SeoVendorCommentScore();
                    value.setVendorCode(kv.getValue().getVendorcode().toUpperCase().trim());
                    value.setSubItemScore(kv.getValue().getSub_item_score());
                    value.setSocre(new BigDecimal(Optional.ofNullable(kv.getValue().getScore()).orElse(0D).toString()));
                    value.setTotalCount(Optional.ofNullable(kv.getValue().getTotalcount()).orElse(0));
                    value.setSubItemScore(kv.getValue().getSub_item_score());
                    value.setStatus(0);
                    marketDBService.insertVendorCommentScore(value);
                }
            } catch (Exception e) {
                log.warn("seoVendorCommentScoreSync", e);
            }
        }
    }

    private SeoHotVendorInformation vendorInformationConvert(BiVendorInformationDto value) {
        try {
            SeoHotVendorInformation item = new SeoHotVendorInformation();
            item.setVendorCode(value.getVendorcode().toUpperCase().trim());
            item.setVendorName(value.getBrand_cn_name());
            item.setCityId(Integer.valueOf(value.getPickupcityid()));
            item.setCityName(value.getPickupcityname());
            item.setPoiCode(value.getPickuplocationcode());
            item.setPoiName(value.getPickuplocationname());
            item.setPoiType(Integer.valueOf(value.getPickuplocationtype()));
            item.setSearchNum(value.getRequestid_cnt());
            item.setStoreNum(value.getStorecnt());
            item.setTenancy(value.getHot_usedays());
            item.setVehicleId(NumberUtils.isNumber(value.getHot_vehiclecode()) ? Long.parseLong(value.getHot_vehiclecode()) : 0L);
            item.setVehicleGroupId(NumberUtils.isNumber(value.getCar_model_group_id()) ? Integer.parseInt(value.getCar_model_group_id()) : 0);
            item.setStatus(0);
            return item;
        } catch (Exception e) {
            log.warn("vendorInformationConvert", e);
            return null;
        }
    }

    private List<BiVendorInformationDto> getBiVendorInformationData() throws Exception {
        long offset = 0;
        long row = 500;
        List<BiVendorInformationDto> result = Lists.newArrayList();
        List<BiVendorInformationDto> temp = null;
        do {
            DaasApiRequestType requestType = new DaasApiRequestType();
            Map<String, Object> params = new HashMap<>();
            params.put("offset", offset);
            params.put("rows", row);
            requestType.setHead(new Head("JXD6bj82yTo2mn1jtCo1xA==", 100043032, "getCdmLogCarOsdTripVendorpageInfoFromsgpDf"));
            requestType.setParams(JsonUtil.toJSONString(params));
            DaasApiResponseType responseType = daasClient.invoke2(requestType);
            CLogUtil.info("getCdmLogCarOsdTripVendorpageInfoFromsgpDf", requestType, responseType);
            if (StringUtils.isNotBlank(responseType.getData())) {
                temp = JsonUtil.parseArray(responseType.getData(), BiVendorInformationDto.class);
                if (CollectionUtils.isNotEmpty(temp)) {
                    result.addAll(temp);
                    offset = temp.stream().map(BiVendorInformationDto::getMysql_id).max(Comparator.comparingLong(o -> o)).get();
                }
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    private List<BiVendorCommentScoreDto> getBiVendorCommentScoreData() throws Exception {
        long offset = 0;
        long row = 500;
        List<BiVendorCommentScoreDto> result = Lists.newArrayList();
        List<BiVendorCommentScoreDto> temp = null;
        do {
            DaasApiRequestType requestType = new DaasApiRequestType();
            Map<String, Object> params = new HashMap<>();
            params.put("offset", offset);
            params.put("rows", row);
            requestType.setHead(new Head("JXD6bj82yTo2mn1jtCo1xA==", 100043032, "getCdmSevCarCommentscoreRecalculateOsdVendorcodeDf"));
            requestType.setParams(JsonUtil.toJSONString(params));
            DaasApiResponseType responseType = daasClient.invoke2(requestType);
            CLogUtil.info("getCdmSevCarCommentscoreRecalculateOsdVendorcodeDf", requestType, responseType);
            if (StringUtils.isNotBlank(responseType.getData())) {
                temp = JsonUtil.parseArray(responseType.getData(), BiVendorCommentScoreDto.class);
                if (CollectionUtils.isNotEmpty(temp)) {
                    result.addAll(temp);
                    offset = temp.stream().map(BiVendorCommentScoreDto::getMysql_id).max(Comparator.comparingLong(o -> o)).get();
                }
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }
}
