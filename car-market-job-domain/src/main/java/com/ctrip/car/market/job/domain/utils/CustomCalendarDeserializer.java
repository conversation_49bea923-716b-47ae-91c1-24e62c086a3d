package com.ctrip.car.market.job.domain.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;
import java.util.Calendar;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CustomCalendarDeserializer extends JsonDeserializer<Calendar> {
    private static final Pattern DATE_PATTERN =
            Pattern.compile("/Date\\((\\d+)([+-]\\d+)?\\)/");

    @Override
    public Calendar deserialize(JsonParser p, DeserializationContext ctxt)
            throws IOException, JsonProcessingException {

        String dateStr = p.getText();
        Matcher matcher = DATE_PATTERN.matcher(dateStr);

        if (matcher.find()) {
            long timestamp = Long.parseLong(matcher.group(1));
            String timezoneOffset = matcher.group(2);

            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(timestamp);

            if (timezoneOffset != null) {
                // 处理时区偏移 (例如 +0800)
                int hours = Integer.parseInt(timezoneOffset.substring(0, 3));
                int minutes = Integer.parseInt(timezoneOffset.substring(3));
                int offsetMillis = (hours * 60 + minutes) * 60 * 1000;
                calendar.set(Calendar.ZONE_OFFSET, offsetMillis);
            }
            return calendar;
        }
        throw new IOException("Invalid date format: " + dateStr);
    }
}