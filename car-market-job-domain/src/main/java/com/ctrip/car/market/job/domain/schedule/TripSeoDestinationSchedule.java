package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.config.JobConfig;
import com.ctrip.car.market.job.domain.dto.CityPoiConfigItem;
import com.ctrip.car.market.job.domain.enums.SeoHotStatusEnums;
import com.ctrip.car.market.job.domain.proxy.GlobalPoiJavaProxy;
import com.ctrip.car.market.job.domain.proxy.OsdBasicDataProxy;
import com.ctrip.car.market.job.domain.service.SeoHotDestinationBusiness;
import com.ctrip.car.market.job.domain.utils.StringFormat;
import com.ctrip.car.market.job.repository.entity.CarKalabCity;
import com.ctrip.car.market.job.repository.entity.SeoHotCityinfo;
import com.ctrip.car.market.job.repository.entity.SeoHotCountryinfo;
import com.ctrip.car.market.job.repository.entity.SeoHotDestinatioinfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.car.osd.basicdataservice.dto.Airport;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.repository.CountryRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.geo.domain.value.Country;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.ctrip.car.market.job.domain.utils.GlobalVariable.*;
import static com.ctrip.car.market.job.domain.utils.GlobalVariable.AIRPORT_CODE;

@Component
public class TripSeoDestinationSchedule {

    private final ILog log = LogManager.getLogger(TripSeoDestinationSchedule.class);

    @Resource
    private MarketDBService marketDBService;

    @Resource
    private CountryRepository countryRepository;

    @Resource
    private CityRepository cityRepository;

    @Resource
    private OsdBasicDataProxy osdBasicDataProxy;

    @Resource
    private GlobalPoiJavaProxy globalPoiJavaProxy;

    @Resource
    private SeoHotDestinationBusiness seoHotDestinationBusiness;

    @Resource
    private JobConfig jobConfig;

    private final String defaultLocale = "en-US";

    private final String logTitle = "TripSeoDestinationSchedule";

    @QSchedule("car.market.seo.pull.hot.destination.sync.klb.city")
    public void task() throws Exception {
        //获取全量海外有资源的城市
        List<CarKalabCity> cityList = marketDBService.queryAllOsdCity();
        if (CollectionUtils.isEmpty(cityList)) {
            return;
        }

        List<SeoHotCountryinfo> dbCountryList = marketDBService.queryAllHotCountry();
        List<SeoHotCityinfo> dbCityList = marketDBService.queryAllHotCity();
        List<SeoHotDestinatioinfo> dbPoiList = marketDBService.queryAllHotPoi();
        Set<Long> countrySet = dbCountryList.stream().map(l -> l.getCountryId().longValue()).collect(Collectors.toSet());
        Set<Long> citySet = dbCityList.stream().map(l -> l.getCityId().longValue()).collect(Collectors.toSet());
        Set<String> airportSet = dbPoiList.stream().map(l -> l.getPoiCode().toLowerCase()).collect(Collectors.toSet());

        List<SeoHotDestinatioinfo> newDestinationList = Lists.newArrayList();
        List<SeoHotCityinfo> newCityList = Lists.newArrayList();
        List<SeoHotCountryinfo> newCountryList = Lists.newArrayList();
        for (CarKalabCity carKalabCity : cityList) {
            if (citySet.contains(carKalabCity.getCityId())) {
                continue;
            }
            //查询对应城市的机场
            Airport airport = osdBasicDataProxy.getCityAirport(carKalabCity.getCityId().intValue(), defaultLocale);
            //没有机场的话跳过
            if (airport == null) {
                log.warn(logTitle, "city no airport:" + carKalabCity.getCityId());
                continue;
            }
            Country country = countryRepository.findOne(carKalabCity.getCountryId());
            City city = cityRepository.findOne(carKalabCity.getCityId());
            if (city == null || country == null) {
                log.warn(logTitle, "city or country is null:" + carKalabCity.getCityId() + "," + carKalabCity.getCountryId());
                continue;
            }
            //新增城市
            newCityList.add(buildCity(country, city));
            citySet.add(city.getId());

            //新增机场
            if (!airportSet.contains(airport.getAirportCode().toLowerCase())) {
                newDestinationList.add(buildDestination(country, city, airport));
                airportSet.add(airport.getAirportCode().toLowerCase());
            }
            //新增国家
            if (!countrySet.contains(country.getId())) {
                newCountryList.add(buildCountry(country));
                countrySet.add(country.getId());
            }
        }
        //运营配置poi
        List<CityPoiConfigItem> poiList = jobConfig.getCityPoiConfigList();
        if (CollectionUtils.isNotEmpty(poiList)) {
            for (CityPoiConfigItem item : poiList) {
                City city = cityRepository.findOne(item.getCityId().longValue());
                if (city == null) {
                    log.warn(logTitle, "city is null:" + item.getCityId());
                    continue;
                }
                Country country = countryRepository.findOne(city.getCountryId());
                if (country == null) {
                    log.warn(logTitle, "country is null:" + city.getCountryId());
                    continue;
                }
                if (!citySet.contains(city.getId())) {
                    newCityList.add(buildCity(country, city));
                    citySet.add(city.getId());
                }
                if (!countrySet.contains(country.getId())) {
                    newCountryList.add(buildCountry(country));
                    countrySet.add(country.getId());
                }
                if (Objects.equals(item.getPoiType(), 1) && StringUtils.isNotEmpty(item.getPoiCode()) && !airportSet.contains(item.getPoiCode().toLowerCase())) {
                    Airport airport = osdBasicDataProxy.getAirportByCode(item.getPoiCode(), defaultLocale);
                    if (airport != null) {
                        newDestinationList.add(buildDestination(country, city, airport));
                        airportSet.add(item.getPoiCode().toLowerCase());
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(newDestinationList)) {
            seoHotDestinationBusiness.batchInsertIntoDestination(newDestinationList);
        }
        if (CollectionUtils.isNotEmpty(newCityList)) {
            seoHotDestinationBusiness.batchInsertIntoCity(newCityList);
        }
        if (CollectionUtils.isNotEmpty(newCountryList)) {
            seoHotDestinationBusiness.batchInsertIntoCountry(newCountryList);
        }
    }

    private Airport getCityDefaultAirport(Long cityId) {
        CityPoiConfigItem item = jobConfig.getCityPoiConfigList().stream().filter(l -> Objects.equals(l.getPoiType(), 1) && Objects.equals(l.getCityId(), cityId.intValue())).findFirst().orElse(null);
        if (item == null) {
            return null;
        }
        List<Airport> list = osdBasicDataProxy.getAirportNoCityId(item.getPoiCode(), defaultLocale);
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    public SeoHotCountryinfo buildCountry(Country country) {
        String countryName = StringFormat.removeSpecialCharacters(country.getEnglishName());
        if (country.getId().equals(TURKEY_ID)) {
            countryName = TURKEY;
        }
        SeoHotCountryinfo seoHotCountryinfo = new SeoHotCountryinfo();
        seoHotCountryinfo.setCountryId(country.getId().intValue());
        seoHotCountryinfo.setStatus(SeoHotStatusEnums.ACTIVE.getCode());
        seoHotCountryinfo.setCountryName(country.getEnglishName());
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(PRE_URL).
                append("to-")
                .append(countryName.toLowerCase()).append("-").append(country.getId())
                .append("/");
        seoHotCountryinfo.setUrl(stringBuilder.toString());
        return seoHotCountryinfo;
    }

    public SeoHotCityinfo buildCity(Country country, City city) {
        SeoHotCityinfo seoHotCityinfo = new SeoHotCityinfo();
        seoHotCityinfo.setCityId(city.getId().intValue());
        seoHotCityinfo.setCityName(city.getEnglishName());
        seoHotCityinfo.setStatus(SeoHotStatusEnums.ACTIVE.getCode());
        seoHotCityinfo.setCountryId(city.getCountryId().intValue());
        seoHotCityinfo.setProvinceId(city.getProvinceId().intValue());
        StringBuilder stringBuilder = new StringBuilder();
        String countryName = StringFormat.removeSpecialCharacters(country.getEnglishName());
        String cityName = StringFormat.removeSpecialCharacters(city.getEnglishName());
        if (city.getCountryId().equals(TURKEY_ID)) {
            countryName = TURKEY;
        }
        stringBuilder.append(PRE_URL)
                .append("to-")
                .append(countryName.toLowerCase()).append("-").append(city.getCountryId())
                .append("/")
                .append(cityName.toLowerCase()).append("-").append(city.getId())
                .append("/");
        seoHotCityinfo.setUrl(stringBuilder.toString());
        return seoHotCityinfo;
    }

    public SeoHotDestinatioinfo buildDestination(Country country, City city, Airport airport) {
        Long poiId = globalPoiJavaProxy.getPoiIdByCode(airport.getAirportCode());
        String countryName = StringFormat.removeSpecialCharacters(country.getEnglishName());
        String cityName = StringFormat.removeSpecialCharacters(city.getEnglishName());
        String airportName = StringFormat.removeSpecialCharacters(airport.getAirportName());
        SeoHotDestinatioinfo seoHotDestinatioinfo = new SeoHotDestinatioinfo();
        seoHotDestinatioinfo.setCityId(city.getId().intValue());
        seoHotDestinatioinfo.setStatus(SeoHotStatusEnums.ACTIVE.getCode());
        StringBuilder stringBuilder = new StringBuilder();
        if (country.getId().equals(TURKEY_ID)) {
            countryName = TURKEY;
        }
        stringBuilder.append(PRE_URL)
                .append("to-")
                .append(countryName.toLowerCase()).append("-").append(country.getId())
                .append("/")
                .append(cityName.toLowerCase()).append("-").append(city.getId())
                .append("/")
                .append(airportName.toLowerCase()).append("-").append(airport.getAirportCode().toLowerCase())
                .append("/");
        seoHotDestinatioinfo.setUrl(stringBuilder.toString());
        seoHotDestinatioinfo.setPoiType(AIRPORT_CODE);
        seoHotDestinatioinfo.setPoiId(Optional.ofNullable(poiId).orElse(0L));
        seoHotDestinatioinfo.setOrderNum(0);
        seoHotDestinatioinfo.setPoiName(airport.getAirportName());
        seoHotDestinatioinfo.setCountryId(country.getId().intValue());
        seoHotDestinatioinfo.setPoiCode(airport.getAirportCode().toUpperCase());
        return seoHotDestinatioinfo;
    }


}
