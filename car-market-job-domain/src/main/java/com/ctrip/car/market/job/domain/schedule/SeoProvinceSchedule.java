package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.enums.SeoHotStatusEnums;
import com.ctrip.car.market.job.domain.service.SeoHotDestinationBusiness;
import com.ctrip.car.market.job.domain.service.SeoHotProvinceBusiness;
import com.ctrip.car.market.job.domain.utils.StringFormat;
import com.ctrip.car.market.job.repository.entity.SeoHotCityinfo;
import com.ctrip.car.market.job.repository.entity.SeoHotProvinceinfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.repository.ProvinceRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.geo.domain.value.Province;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.*;

import static com.ctrip.car.market.job.domain.utils.GlobalVariable.*;

/**
 * Author: dhui zou
 * Date: 2025/8/21
 * Description: SEO生成省份页面
 */
@Component
public class SeoProvinceSchedule {

    private final ILog log = LogManager.getLogger(SeoProvinceSchedule.class);

    @Resource
    private MarketDBService marketDBService;

    @Resource
    private ProvinceRepository provinceRepository;

    @Resource
    private CityRepository cityRepository;

    @Resource
    private SeoHotProvinceBusiness seoHotProvinceBusiness;

    @Resource
    private SeoHotDestinationBusiness seoHotDestinationBusiness;

    private final String logTitle = "SeoProvinceSchedule";

    @QSchedule("car.market.seo.province.sync")
    public void task() throws Exception {
        log.info(logTitle, "Starting province SEO page generation task");

        // 查询所有城市
        List<SeoHotCityinfo> hotCityList = marketDBService.queryAllHotCity();
        if (CollectionUtils.isEmpty(hotCityList)) {
            log.warn(logTitle, "No hot cities found");
            return;
        }

        // 从城市数据中提取省份信息并去重、城市页面补充省份信息
        Map<Integer, Province> provinceMap = extractAndDeduplicateProvinces(hotCityList);
        // 获取数据库中已存在的 有效省份记录
        List<SeoHotProvinceinfo> existingProvinceList = seoHotProvinceBusiness.getSeoHotProvinceinfoList(true);
        Set<Integer> existingProvinceIds = new HashSet<>();
        for (SeoHotProvinceinfo existingProvince : existingProvinceList) {
            if (existingProvince.getProvinceId() != null) {
                existingProvinceIds.add(existingProvince.getProvinceId());
            }
        }
        // 过滤已存在的省份，只保留新的省份
        List<SeoHotProvinceinfo> newProvinceList = Lists.newArrayList();

        for (Province province : provinceMap.values()) {
            if (!existingProvinceIds.contains(province.getId().intValue())) {
                // 数据库中不存在该省份，创建新记录
                SeoHotProvinceinfo provincePage = buildProvinceDestination(province);
                if (provincePage != null) {
                    newProvinceList.add(provincePage);
                }
            } else {
                log.debug(logTitle, "Province already exists, skipping: " + province.getEnglishName() + " (ID: " + province.getId() + ")");
            }
        }

        // 批量插入新的省份页面
        if (CollectionUtils.isNotEmpty(newProvinceList)) {
            log.info(logTitle, "Inserting " + newProvinceList.size() + " new province pages");
            batchInsertProvinces(newProvinceList);
        } else {
            log.info(logTitle, "No new provinces to insert");
        }
    }

    /**
     * 从城市列表中提取省份信息并去重、将省份ID设置到城市信息中
     */
    private Map<Integer, Province> extractAndDeduplicateProvinces(List<SeoHotCityinfo> cityList) throws SQLException {
        Map<Integer, Province> provinceMap = new HashMap<>();
        List<SeoHotCityinfo> updateCityList = new ArrayList<>();

        for (SeoHotCityinfo cityInfo : cityList) {
            try {
                City city = cityRepository.findOne(cityInfo.getCityId().longValue());
                if (city == null) {
                    log.warn(logTitle, "City not found for cityId: " + cityInfo.getCityId());
                    continue;
                }
                // 获取省份信息
                Province province = provinceRepository.findOne(city.getProvinceId());
                if (province == null) {
                    log.warn(logTitle, "Province not found for city: " + city.getId());
                    continue;
                }
                // 将省份ID设置到城市信息中
                Integer provinceId = province.getId().intValue();

                // 检查城市是否需要更新省份ID  如果城市没有getProvinceId 需更新
                if (cityInfo.getProvinceId() == null ||cityInfo.getProvinceId() == 0 ) {
                    cityInfo.setProvinceId(provinceId.intValue());
                    updateCityList.add(cityInfo);
                    log.debug(logTitle, "Updated provinceId for city: " + cityInfo.getCityId() + " to province: " + provinceId);
                }

                // 构建省份信息对象（去重）
                if (!provinceMap.containsKey(provinceId)) {
                    provinceMap.put(provinceId, province);
                    log.debug(logTitle, "Added province: " + province.getEnglishName() + " (ID: " + provinceId + ")");
                }
            } catch (Exception e) {
                log.warn(logTitle, "Error processing city: " + cityInfo.getCityId());
            }
        }
        // 批量更新城市的省份ID
        if (CollectionUtils.isNotEmpty(updateCityList)) {
            seoHotDestinationBusiness.batchUpdateCity(updateCityList);
            log.info(logTitle, "Updated provinceId for " + updateCityList.size() + " cities");
        }
        log.info(logTitle, "Extracted " + provinceMap.size() + " unique provinces");
        return provinceMap;
    }

    /**
     * 构建省份页面数据
     */
    private SeoHotProvinceinfo buildProvinceDestination(Province province) {
        try {
            SeoHotProvinceinfo provinceinfo = new SeoHotProvinceinfo();

            // 设置基本信息
            provinceinfo.setProvinceId(province.getId().intValue());
            provinceinfo.setProvinceName(province.getEnglishName());
            provinceinfo.setCountryId(province.getCountryId().intValue());
            provinceinfo.setStatus(SeoHotStatusEnums.ACTIVE.getCode());

            // 构建省份页面URL：https://tw.trip.com/carhire/province/province-id/
            String provinceUrl = buildProvinceUrl(province);
            provinceinfo.setUrl(provinceUrl);

            return provinceinfo;
        } catch (Exception e) {
            log.warn(logTitle, "Failed to build province destination for: " + province.getId());
            return null;
        }
    }

    /**
     * 构建省份URL
     */
    private String buildProvinceUrl(Province province) {
        StringBuilder urlBuilder = new StringBuilder();
        String provinceName = StringFormat.removeSpecialCharacters(province.getEnglishName()).toLowerCase();

        urlBuilder.append(PRE_URL)
                .append("province/")
                .append(provinceName).append("-").append(province.getId())
                .append("/");

        return urlBuilder.toString();
    }

    /**
     * 批量插入省份数据
     */
    private void batchInsertProvinces(List<SeoHotProvinceinfo> provinceList) {
        try {
            seoHotProvinceBusiness.batchInsertIntoProvinces(provinceList);
            log.info(logTitle, "batchInsertProvinces: ");
        } catch (Exception e) {
            log.error(logTitle, "Failed to batch insert provinces");
        }
    }

}