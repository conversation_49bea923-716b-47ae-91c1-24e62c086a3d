package com.ctrip.car.market.job.domain.mq.mkt;


import com.ctrip.car.market.job.domain.dto.coupon.CouponUseMessage;
import com.ctrip.car.market.job.domain.service.MktCouponHandleService;
import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.car.market.job.domain.utils.Metrics;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.base.Strings;
import credis.java.client.util.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * 发放优惠券、收藏优惠券(机票、酒店券专用)
 */

@Component
public class MktCouponCollectListen {
    @Autowired
    MktCouponHandleService mktCouponHandleService;
    private static final ILog log = LogManager.getLogger(MktCouponCollectListen.class);

     @QmqConsumer(prefix = "mkt.promocode.receivecouponcode.car", consumerGroup = "car.coupon.*********.qmq.consumer")
    public void changeMessage(Message message) {
        String data = message.getStringProperty("mkt.promocode.receivecouponcode.car");
        if (Strings.isNullOrEmpty(data)) {
            return;
        }
        try {
            log.info("MktCouponCollectListen",data);
            CouponUseMessage mktCouponQMQDTo = JsonUtil.fromJson(data, CouponUseMessage.class);
            mktCouponQMQDTo.setUseStatus(0);
            boolean result = mktCouponHandleService.sendQunarCouponQmq(mktCouponQMQDTo,"car");
            Metrics.build().withTag("result", result + "").withTag("mktQMQType", "car").recordOne("MktCouponListen");
        } catch (Exception e) {
            log.error("car", e);
        }
    }
//    @QmqConsumer(prefix = "mkt.promocode.receivecouponcode.airportTransfer", consumerGroup = "car.coupon.*********.qmq.consumer")
//    public void changeMessageAirportTransfer(Message message) {
//        String data = message.getStringProperty("mkt.promocode.receivecouponcode.airportTransfer");
//        if (Strings.isNullOrEmpty(data)) {
//            return;
//        }
//        try {
//            log.info("changeMessageAirportTransfer",data);
//            CouponUseMessage mktCouponQMQDTo = JsonUtil.fromJson(data, CouponUseMessage.class);
//            mktCouponQMQDTo.setUseStatus(0);
//            boolean result = mktCouponHandleService.sendQunarCouponQmq(mktCouponQMQDTo,"airportTransfer");
//            Metrics.build().withTag("result", result + "").withTag("mktQMQType", "airportTransfer").recordOne("MktCouponListen");
//        } catch (Exception e) {
//            log.error("airportTransfer", e);
//        }
//    }
//    @QmqConsumer(prefix = "mkt.promocode.receivecouponcode.stationTransfer", consumerGroup = "car.coupon.*********.qmq.consumer")
//    public void changeMessageStationTransfer(Message message) {
//        String data = message.getStringProperty("mkt.promocode.receivecouponcode.stationTransfer");
//        if (Strings.isNullOrEmpty(data)) {
//            return;
//        }
//        try {
//            log.info("changeMessageStationTransfer",data);
//            CouponUseMessage mktCouponQMQDTo = JsonUtil.fromJson(data, CouponUseMessage.class);
//            mktCouponQMQDTo.setUseStatus(0);
//            boolean result = mktCouponHandleService.sendQunarCouponQmq(mktCouponQMQDTo,"stationTransfer");
//            Metrics.build().withTag("result", result + "").withTag("mktQMQType", "stationTransfer").recordOne("MktCouponListen");
//        } catch (Exception e) {
//            log.error("stationTransfer", e);
//        }
//    }
//    @QmqConsumer(prefix = "mkt.promocode.receivecouponcode.carDriver", consumerGroup = "car.coupon.*********.qmq.consumer")
//    public void changeMessageCarDriver(Message message) {
//        String data = message.getStringProperty("mkt.promocode.receivecouponcode.carDriver");
//        if (Strings.isNullOrEmpty(data)) {
//            return;
//        }
//        try {
//            log.info("carDriver",data);
//            CouponUseMessage mktCouponQMQDTo = JsonUtil.fromJson(data, CouponUseMessage.class);
//            mktCouponQMQDTo.setUseStatus(0);
//            boolean result = mktCouponHandleService.sendQunarCouponQmq(mktCouponQMQDTo,"carDriver");
//            Metrics.build().withTag("result", result + "").withTag("mktQMQType", "carDriver").recordOne("MktCouponListen");
//        } catch (Exception e) {
//            log.error("carDriver", e);
//        }
//    }
//    @QmqConsumer(prefix = "mkt.promocode.receivecouponcode.taxis", consumerGroup = "car.coupon.*********.qmq.consumer")
//    public void changeMessageTaxis(Message message) {
//        String data = message.getStringProperty("mkt.promocode.receivecouponcode.taxis");
//        if (Strings.isNullOrEmpty(data)) {
//            return;
//        }
//        try {
//            log.info("taxis",data);
//            CouponUseMessage mktCouponQMQDTo = JsonUtil.fromJson(data, CouponUseMessage.class);
//            mktCouponQMQDTo.setUseStatus(0);
//            boolean result = mktCouponHandleService.sendQunarCouponQmq(mktCouponQMQDTo,"taxis");
//            Metrics.build().withTag("result", result + "").withTag("mktQMQType", "taxis").recordOne("MktCouponListen");
//        } catch (Exception e) {
//            log.error("taxis", e);
//        }
//    }
}