package com.ctrip.car.market.job.domain.mq.mkt;


import com.ctrip.car.market.job.domain.dto.coupon.CouponUseMessage;
import com.ctrip.car.market.job.domain.service.MktCouponHandleService;
import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.car.market.job.domain.utils.Metrics;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.base.Strings;
import credis.java.client.util.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/***
 * 取消作废优惠券
 */


@Component
public class MktCouponRestoreListen {
    @Autowired
    MktCouponHandleService mktCouponHandleService;
    private static final ILog log = LogManager.getLogger(MktCouponRestoreListen.class);

    @QmqConsumer(prefix = "mkt.promocode.restore.couponuseentity.created", consumerGroup = "car.coupon.100043032.qmq.consumer")
    public void changeMessage(Message message) {
        String data = message.getStringProperty("mkt.promocode.restore.couponuseentity.created");
        if (Strings.isNullOrEmpty(data)) {
            return;
        }
        try {
            log.info("MktCouponRestoreListen",data);
            CouponUseMessage mktCouponQMQDTo = JsonUtil.fromJson(data, CouponUseMessage.class);
            boolean result = mktCouponHandleService.sendQunarCouponQmq(mktCouponQMQDTo,"MktCouponRestoreListen");
            Metrics.build().withTag("result", result + "").withTag("mktQMQType", "MktCouponRestoreListen").recordOne("MktCouponListen");
        } catch (Exception e) {
            log.error("MktCouponRestoreListen", e);
        }
    }

}