package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.framework.ucs.common.util.StringUtils;
import com.ctrip.gs.globalpoi.soa.contract.*;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class GlobalPoiNameJavaProxy {
    private final ILog log = LogManager.getLogger(GlobalPoiNameJavaProxy.class);

    private GlobalPoiJavaClient client = GlobalPoiJavaClient.getInstance();

    private static final Integer MAX_NUMBER = 500;

    public Map<Long, String> getPoiNamesByIds(List<Long> poiIds) {
        Map<Long, String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(poiIds)) {
            return result;
        }

        try {
            int limit = (poiIds.size() + MAX_NUMBER - 1) / MAX_NUMBER;
            Stream.iterate(0, n -> n + 1).limit(limit).forEach(i -> {
                GetPoiDetailRequestType requestType = new GetPoiDetailRequestType();
                List<Long> batchPoiIds = poiIds.stream()
                        .skip((long) i * MAX_NUMBER)
                        .limit(MAX_NUMBER)
                        .collect(Collectors.toList());

                requestType.setPoiIds(batchPoiIds);
                try {
                    GetPoiDetailResponseType responseType = client.getPoiDetail(requestType);
                    if (responseType != null && CollectionUtils.isNotEmpty(responseType.getResult())) {
                        responseType.getResult().forEach(poiDetail -> {
                            if (poiDetail != null && poiDetail.getPoiId() != null) {
                                String poiName = poiDetail.getEName();
                                if (StringUtils.isNotEmpty(poiName)) {
                                    result.put(poiDetail.getPoiId(), poiName);
                                }
                            }
                        });
                    }
                } catch (Exception e) {
                    log.warn("query poi detail failed for batch starting at index " + (i * MAX_NUMBER), e);
                }
            });
            return result;
        } catch (Exception e) {
            log.warn("query poi detail failed", e);
            return result;
        }
    }

}
