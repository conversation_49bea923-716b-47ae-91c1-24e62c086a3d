package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.crossrecommend.service.contract.dto.RecommendProductDTO;
import com.ctrip.car.market.crossrecommend.service.contract.servicetype.CarRecommendProductRequestType;
import com.ctrip.car.market.job.domain.utils.RedisUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
import org.powermock.modules.junit4.PowerMockRunner;


/**
 * <AUTHOR> yu
 * @date 2025/8/20 19:21
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({
        RedisUtil.class
})
@SuppressStaticInitializationFor({
        "com.ctrip.car.market.job.domain.utils.RedisUtil"
})
public class UTUtilsTest {



    @Test
    public void testsendToRedis1(){
        Boolean key = true;
        for (int i=1;i<=1000;i++) {
            UTUtils.ut(i);
        }
        Assert.assertTrue(key);
    }
}
