package com.ctrip.car.market.job.domain.service.seo;

import com.ctrip.car.market.job.common.entity.seo.SeoHotProvinceinfoDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotProvinceinfoMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotProvinceinfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllSeoHotProvinceServiceTest {

    @Mock
    private MarketDBService mockService;
    @Mock
    private SeoHotProvinceinfoMapper mockMapper;

    @InjectMocks
    private QueryAllSeoHotProvinceService queryAllSeoHotProvinceService;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure MarketDBService.queryAllHotCity(...).
        final SeoHotProvinceinfo seoHotProvinceinfo = new SeoHotProvinceinfo();
        seoHotProvinceinfo.setId(0L);
        seoHotProvinceinfo.setProvinceId(0);
        seoHotProvinceinfo.setProvinceName("test");
        seoHotProvinceinfo.setCountryId(0);
        seoHotProvinceinfo.setUrl("url");
        final List<SeoHotProvinceinfo> seoHotProvinceinfos = Arrays.asList(seoHotProvinceinfo);
        when(mockService.queryAllSeoHotProvince()).thenReturn(seoHotProvinceinfos);

        // Configure SeoHotCityinfoMapper.to(...).
        final SeoHotProvinceinfoDO seoHotProvinceinfoDO = new SeoHotProvinceinfoDO();
        seoHotProvinceinfoDO.setId(0L);
        seoHotProvinceinfoDO.setProvinceId(0);
        seoHotProvinceinfoDO.setProvinceName("cityName");
        seoHotProvinceinfoDO.setCountryId(0);
        seoHotProvinceinfoDO.setUrl("url");
        when(mockMapper.to(any(SeoHotProvinceinfo.class))).thenReturn(seoHotProvinceinfoDO);

        // Run the test
        Assert.assertTrue(CollectionUtils.isNotEmpty(queryAllSeoHotProvinceService.load("area", 0)));
    }

    @Test
    public void testLoad_MarketDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllSeoHotProvince()).thenReturn(Collections.emptyList());

        // Run the test
        final List<SeoHotProvinceinfoDO> result = queryAllSeoHotProvinceService.load("area", 0);

        // Verify the results
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_MarketDBServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryAllSeoHotProvince()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllSeoHotProvinceService.load("area", 0))
                .isInstanceOf(SQLException.class);
    }
}
