# GC错误解决方案

## 问题描述
在运行单元测试时可能出现GC（垃圾回收）错误，特别是使用PowerMock进行静态Mock时。

## 解决方案

### 1. JVM参数优化
在IDE中设置以下JVM参数：

#### 推荐配置（G1GC）：
```
-Xmx1g -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+DisableExplicitGC -XX:+UseStringDeduplication
```

#### 备选配置（CMS）：
```
-Xmx1g -Xms512m -XX:+UseConcMarkSweepGC -XX:+CMSClassUnloadingEnabled -XX:+CMSPermGenSweepingEnabled
```

#### 最小配置：
```
-Xmx512m -Xms256m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+DisableExplicitGC
```

### 2. 测试代码优化
- 已添加 `@After` 方法进行资源清理
- 使用 `Collections.emptyList()` 替代 `new ArrayList<>()`
- 减少不必要的对象创建
- 添加 `System.gc()` 强制垃圾回收

### 3. IDE设置
#### IntelliJ IDEA：
1. 打开 Run/Debug Configurations
2. 选择对应的测试配置
3. 在 VM options 中添加上述JVM参数

#### Eclipse：
1. 右键测试类 -> Run As -> Run Configurations
2. 选择 Arguments 标签
3. 在 VM arguments 中添加上述JVM参数

### 4. Maven配置
在 `pom.xml` 中添加：
```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-surefire-plugin</artifactId>
    <version>2.22.2</version>
    <configuration>
        <argLine>-Xmx1g -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200</argLine>
    </configuration>
</plugin>
```

### 5. 监控和调试
如果问题仍然存在，可以添加以下JVM参数进行监控：
```
-XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -Xloggc:gc.log
```

## 注意事项
1. PowerMock会创建大量的字节码，需要更多内存
2. 静态Mock可能导致类加载器泄漏
3. 建议在测试完成后重启JVM
4. 如果内存不足，可以增加堆内存大小

