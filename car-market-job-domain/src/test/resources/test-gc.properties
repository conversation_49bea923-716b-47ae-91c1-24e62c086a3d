# JVMåæ°å»ºè®®ï¼ç¨äºåå°GCéè¯¯
# å¨IDEä¸­è®¾ç½®ä»¥ä¸JVMåæ°ï¼
# -Xmx512m -Xms256m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+DisableExplicitGC

# æèä½¿ç¨ä»¥ä¸åæ°ï¼
# -Xmx1g -Xms512m -XX:+UseConcMarkSweepGC -XX:+CMSClassUnloadingEnabled -XX:+CMSPermGenSweepingEnabled

# å¯¹äºPowerMockæµè¯ï¼å»ºè®®ä½¿ç¨ï¼
# -Xmx1g -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+DisableExplicitGC -XX:+UseStringDeduplication

