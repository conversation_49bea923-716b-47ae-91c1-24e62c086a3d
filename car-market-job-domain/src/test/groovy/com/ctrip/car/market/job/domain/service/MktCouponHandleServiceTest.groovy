package com.ctrip.car.market.job.domain.service

import com.ctrip.car.market.job.domain.config.JobConfig
import com.ctrip.car.market.job.domain.dto.coupon.CouponUseMessage
import com.ctrip.car.market.job.domain.dto.coupon.QunarUBTDateDTO
import com.ctrip.car.market.job.domain.proxy.PromotionProxy
import com.ctrip.car.market.job.domain.utils.RedisUtil
import com.ctrip.framework.clogging.agent.log.ILog
import com.ctrip.soa.platform.account.promocodeservice.message.v1.GetPromotionStrategyResponseType
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import qunar.tc.qmq.Message
import qunar.tc.qmq.MessageProducer
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import static com.ctrip.car.market.job.domain.constant.CommonConstant.QUNAR_UBT_USER_INFO_TOPIC
import static org.mockito.Mockito.*

class MktCouponHandleServiceTest extends Specification {
    @Mock
    MessageProducer messageProducer
    @Mock
    PromotionProxy promotionProxy
    @Mock
    JobConfig jobConfig
    @InjectMocks
    MktCouponHandleService mktCouponHandleService

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test send Qunar Coupon Qmq"() {
        given:
        when(messageProducer.generateMessage(anyString())).thenReturn(null)
        when(promotionProxy.getPromotionStrategy(anyInt())).thenReturn(new GetPromotionStrategyResponseType())
        when(jobConfig.getQunarPromotionUseLines()).thenReturn([0])
        when(jobConfig.getPromotionCategoryformCacheExpired()).thenReturn(0)

        when:
        boolean result1= mktCouponHandleService.sendQunarCouponQmq(null, "mktQMQType")
        boolean result2 = mktCouponHandleService.sendQunarCouponQmq(new CouponUseMessage(), "mktQMQType")
        boolean result3 = mktCouponHandleService.sendQunarCouponQmq(new CouponUseMessage(userID: "12",orderID: "orderID",couponCode: "couponCode"), "mktQMQType")

        then:
        result1 == false
        result2 == false
        result3 == false
    }

    def "test send QMQ"() {
        given:
        when(messageProducer.generateMessage(anyString())).thenReturn(mock(Message.class));

        when:
        mktCouponHandleService.sendQMQ(new QunarUBTDateDTO("userID", "userName", "couponCode", 0, 0, "orderID", "pageID", "distributionChannelID", "ubtClickENName", "ubtClickName", "sourceFrom", 0, "appId", "keyId", "bu"))

        then:
        false==false
    }

    def "test Judge Line Is Car"() {
        given:
        when(promotionProxy.getPromotionStrategy(anyInt())).thenReturn(new GetPromotionStrategyResponseType())
        when(jobConfig.getQunarPromotionUseLines()).thenReturn([0])
        when(jobConfig.getPromotionCategoryformCacheExpired()).thenReturn(0)

        when:
        boolean result1 = mktCouponHandleService.JudgeLineIsCar(new CouponUseMessage(useProductLineIDs: null,userProductlineIDs: "18",useProductLineID: null), "mktQMQType")
        boolean result2 = mktCouponHandleService.JudgeLineIsCar(new CouponUseMessage(useProductLineIDs: null,userProductlineIDs: null,useProductLineID: 11), "mktQMQType")
        boolean result3 = mktCouponHandleService.JudgeLineIsCar(new CouponUseMessage(useProductLineIDs: null,userProductlineIDs: "18",), "mktQMQType")

        then:
        result1 == false
        result2 == false
        result3 == false
    }

//    def "test Judge Line Is Qunar Car"() {
//        given:
//        when(promotionProxy.getPromotionStrategy(anyInt())).thenReturn(new GetPromotionStrategyResponseType())
//        when(jobConfig.getPromotionCategoryformCacheExpired()).thenReturn(0)
//
//        when:
//        boolean result = mktCouponHandleService.JudgeLineIsQunarCar(0, "mktQMQType")
//
//        then:
//        result == false
//    }

    def "test convert Qunar User Id"() {
        when:
        String result1 = mktCouponHandleService.convertQunarUserId("Q_userId")
        String result2 = mktCouponHandleService.convertQunarUserId("userId")
        String result3 = mktCouponHandleService.convertQunarUserId(null)

        then:
        result1 == "userId"
        result2 == "erId"
        result3==""

    }

    def "test convert TO Qunar UBT Date DTO"() {
        when:
        QunarUBTDateDTO result = mktCouponHandleService.convertTOQunarUBTDateDTO(new CouponUseMessage())

        then:
        result.getDateType()==0
    }
}

