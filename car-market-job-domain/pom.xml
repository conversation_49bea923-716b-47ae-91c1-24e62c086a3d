<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>car-market-job</artifactId>
        <groupId>com.ctrip.car.market</groupId>
        <version>1.0.14</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>car-market-job-domain</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.soa.platform.members.geolocation</groupId>
            <artifactId>geolocationservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.gs.dest</groupId>
            <artifactId>dest-content-contract</artifactId>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>car-market-job-proxy</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>error_prone_annotations</artifactId>
                    <groupId>com.google.errorprone</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>car-market-job-repository</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>car-market-job-common</artifactId>
        </dependency>

        <dependency>
            <groupId>qunar.tc.qschedule</groupId>
            <artifactId>qschedule-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>error_prone_annotations</artifactId>
                    <groupId>com.google.errorprone</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>compiler</artifactId>
                    <groupId>com.github.spullara.mustache.java</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>qunar.common</groupId>
            <artifactId>common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qunar.pay</groupId>
            <artifactId>qunar-utils-external</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.sd</groupId>
            <artifactId>carcache-all</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework</groupId>
            <artifactId>canal-json</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.market.v1</groupId>
            <artifactId>promocode-client-contract</artifactId>
            <version>2.3.11</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.spullara.mustache.java</groupId>
                    <artifactId>compiler</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.sd.distributor.gateway</groupId>
            <artifactId>distributor-gateway-api</artifactId>
            <version>1.0.6</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.car.osd.framework</groupId>
                    <artifactId>dto</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.spullara.mustache.java</groupId>
                    <artifactId>compiler</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.vavr</groupId>
            <artifactId>vavr</artifactId>
            <version>0.10.4</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car</groupId>
            <artifactId>coupon-common</artifactId>
            <version>3.2.6</version>
            <exclusions>
                <exclusion>
                    <artifactId>checker-qual</artifactId>
                    <groupId>org.checkerframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>baiji-rpc-server</artifactId>
                    <groupId>com.ctriposs.baiji</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>baiji-rpc-client</artifactId>
                    <groupId>com.ctriposs.baiji</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency><!--http代理-->
            <groupId>com.ctrip.car.market-third-support</groupId>
            <artifactId>market-third-support-common</artifactId>
            <version>1.2.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.market.lat.open</groupId>
            <artifactId>trip-open-service-contract</artifactId>
            <version>1.1.2</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.market</groupId>
            <artifactId>mktapi-admanage-api</artifactId>
            <version>0.0.37</version>
            <exclusions>
                <exclusion>
                    <artifactId>compiler</artifactId>
                    <groupId>com.github.spullara.mustache.java</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.market</groupId>
            <artifactId>mktapi-adinsights-api</artifactId>
            <version>0.0.8</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.market</groupId>
                    <artifactId>mktapi-common-entity</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.infosec.kms</groupId>
            <artifactId>kms-sdk</artifactId>
            <version>1.0.4</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>market-crossrecommend-contract</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.customer</groupId>
            <artifactId>customer-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.flight.intl.common</groupId>
            <artifactId>metric-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.ibu.platform</groupId>
            <artifactId>ibu-shark-sdk</artifactId>
        </dependency>
        <!--翻译-->
        <dependency>
            <groupId>com.ctrip.car.osd.translate</groupId>
            <artifactId>client</artifactId>
        </dependency>

        <!--汇率-->
        <dependency>
            <groupId>com.ctrip.soa.platform.accounting.exchangerateservice.v1</groupId>
            <artifactId>exchangerateservice</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.tour.ai</groupId>
            <artifactId>one-service-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.car.order.sdneworderservice.v1</groupId>
            <artifactId>sdneworderservice</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.basebiz</groupId>
            <artifactId>account-service-client</artifactId>
        </dependency>

        <!--shopping-->
        <dependency>
            <groupId>com.ctrip.soa.car.sd.carosdshoppingserviceapi.v1</groupId>
            <artifactId>carosdshoppingserviceapi</artifactId>
            <version>0.0.65</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.car.osd.carosdbasicdataservice.v1</groupId>
            <artifactId>carosdbasicdataservice</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.igt.geo</groupId>
            <artifactId>geo-service-client</artifactId>
            <version>1.4.19</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.order</groupId>
            <artifactId>query-context-facade</artifactId>
            <version>1.1.41</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.market.publicplatform.soaclient</groupId>
            <artifactId>soaclient-alipaytoken</artifactId>
            <version>0.0.10</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.sysdev</groupId>
            <artifactId>daas-client-soa</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>key-escrow-infra</artifactId>
                    <groupId>com.ctrip.infosec.kms</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>kms-sdk-common</artifactId>
                    <groupId>com.ctrip.infosec.kms</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-core</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.car.osd.notificationcenter.v1</groupId>
            <artifactId>carosdnotificationcenter</artifactId>
            <version>2.19.30</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.21394</groupId>
            <artifactId>seo-platform-manager</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.30408</groupId>
            <artifactId>car-commodity-vendor-query-service</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>soa-common</artifactId>
                    <groupId>com.ctrip.car.commodity.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.payment.cwallet</groupId>
            <artifactId>caccount-base-tx-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.risk</groupId>
            <artifactId>safetrip-unified-access-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.arch</groupId>
            <artifactId>distlock-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.ckafka</groupId>
            <artifactId>client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.di.data</groupId>
            <artifactId>cKafkaSerialization</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.di.data</groupId>
            <artifactId>topic-bdai-ubt-client-edw</artifactId>
            <version>0.0.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>avro</artifactId>
                    <groupId>org.apache.avro</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.di.data</groupId>
            <artifactId>ubtEventTypeConverter</artifactId>
            <version>0.0.2</version>
        </dependency>

    </dependencies>
</project>
