<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>osd-bom</artifactId>
        <groupId>com.ctrip.car.osd</groupId>
        <version>1.10.8</version>
    </parent>

    <modules>
        <module>car-market-job-api</module>
        <module>car-market-job-common</module>
        <module>car-market-job-domain</module>
        <module>car-market-job-proxy</module>
        <module>car-market-job-repository</module>
    </modules>

    <groupId>com.ctrip.car.market</groupId>
    <artifactId>car-market-job</artifactId>
    <version>1.0.14</version>
    <packaging>pom</packaging>

    <name>car-market-job</name>

    <properties>
        <caffeine.version>2.8.1</caffeine.version>
        <project.version>1.0.0</project.version>
        <osd-framework.version>2.14.26</osd-framework.version>
        <version>2.24.25</version>
        <isd.thirdparty.version>1.0.1</isd.thirdparty.version>
        <ubt.schema.version>1</ubt.schema.version>
        <mapstruct.version>1.2.0.Final</mapstruct.version>
        <qunar.utils.version>1.0.3</qunar.utils.version>
        <powermock.version>1.7.4</powermock.version>
        <jacoco.version>0.8.5</jacoco.version>
        <es-version>7.5.2</es-version>
        <carcache.version>1.0.24</carcache.version>
        <one.service.version>1.0.1</one.service.version>
        <sdneworderservice.version>2.4.19</sdneworderservice.version>
        <account.service.version>0.0.17</account.service.version>
        <payment.cwallet.version>0.0.7</payment.cwallet.version>
        <safetrip.unified.version>1.0.3</safetrip.unified.version>
        <market.common.version>2.0.21</market.common.version>
        <credis.version>4.4.6</credis.version>
        <qconfig.version>1.100.69-ctrip</qconfig.version>
        <daas.version>1.0.10</daas.version>
        <servertrace.version>********</servertrace.version>
        <seo.platform.version>0.1.68</seo.platform.version>
        <commodity.vendor.version>1.0.10</commodity.vendor.version>
        <ckafka.version>0.2.0</ckafka.version>
        <!--sonar 配置忽略规则-->
        <sonar.exclusions>
            **/*RequestType.java,
            **/*ResponseType.java,
            **/*Dao.java,
            **/*PO.java,
            **/*BO.java,
            **/*QO.java,
            **/*DO.java,
            **/*DTO.java,
            **/*CO.java,
            **/*Consts.java,
            **/*Enum.java,
        </sonar.exclusions>

        <!--Jacoco settings -->
        <jacoco.haltOnFailure>false</jacoco.haltOnFailure>
        <jacoco.skip>false</jacoco.skip>
        <!-- sonar JACOCO properties -->
        <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
        <sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>
        <!--        <sonar.jacoco.reportPaths>${project.basedir}/code-coverage/jacoco.exec</sonar.jacoco.reportPaths>-->
<!--        <sonar.language>java</sonar.language>-->

        <!-- 排除不需要做jacoco覆盖率统计的文件夹或文件 -->
        <sonar.coverage.exclusions>
            **/contract/**, **/consts/**, **/test/**, **/annotations/**,**/exception/**, **/dao/**, **/soa/**,
            **/mapper/**
        </sonar.coverage.exclusions>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ctrip.framework.ckafka</groupId>
                <artifactId>client</artifactId>
                <version>${ckafka.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.framework.ckafka</groupId>
                <artifactId>codec</artifactId>
                <version>${ckafka.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.30408</groupId>
                <artifactId>car-commodity-vendor-query-service</artifactId>
                <version>${commodity.vendor.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.21394</groupId>
                <artifactId>seo-platform-manager</artifactId>
                <version>${seo.platform.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.platform.members.geolocation</groupId>
                <artifactId>geolocationservice</artifactId>
                <version>1.1.40</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.gs.dest</groupId>
                <artifactId>dest-content-contract</artifactId>
                <version>0.0.57</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.framework.ubt</groupId>
                <artifactId>servertrace-sdk</artifactId>
                <version>${servertrace.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.sysdev</groupId>
                <artifactId>daas-client-soa</artifactId>
                <version>${daas.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.credis</groupId>
                <artifactId>credis</artifactId>
                <version>${credis.version}</version>
            </dependency>

            <dependency>
                <groupId>qunar.tc.qconfig</groupId>
                <artifactId>qconfig-client</artifactId>
                <version>${qconfig.version}</version>
            </dependency>

            <dependency>
                <groupId>qunar.tc.qconfig</groupId>
                <artifactId>qconfig-common</artifactId>
                <version>${qconfig.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${es-version}</version>
            </dependency>

            <!-- 项目模块依赖 -->
            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>car-market-job-proxy</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>car-market-job-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>car-market-job-domain</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>car-market-job-repository</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>car-market-job-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 海外基础框架模块 -->
            <dependency>
                <groupId>com.ctrip.car.osd.framework</groupId>
                <artifactId>dto</artifactId>
                <version>${osd-framework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.osd.framework</groupId>
                <artifactId>soa-client</artifactId>
                <version>${osd-framework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.osd.framework</groupId>
                <artifactId>common</artifactId>
                <version>${osd-framework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.osd.framework</groupId>
                <artifactId>cache</artifactId>
                <version>${osd-framework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.osd.framework</groupId>
                <artifactId>dal</artifactId>
                <version>${osd-framework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.osd.framework</groupId>
                <artifactId>redis</artifactId>
                <version>${osd-framework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.osd.framework</groupId>
                <artifactId>aspectj-ctw</artifactId>
                <version>${osd-framework.version}</version>
            </dependency>

            <!--埋点-->
            <dependency>
                <groupId>com.ctrip.muise.schema</groupId>
                <artifactId>ubt.servercustom.created-schema</artifactId>
                <version>${ubt.schema.version}</version>
            </dependency>

            <!--qunar工具-->
            <dependency>
                <groupId>com.qunar.pay</groupId>
                <artifactId>qunar-utils-external</artifactId>
                <version>${qunar.utils.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jacoco</groupId>
                <artifactId>org.jacoco.agent</artifactId>
                <version>${jacoco.version}</version>
                <classifier>runtime</classifier>
            </dependency>

            <dependency>
                <groupId>org.codehaus.sonar-plugins.java</groupId>
                <artifactId>sonar-jacoco-listeners</artifactId>
                <version>1.2</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.ctrip.di.data</groupId>
                <artifactId>abtestclient</artifactId>
                <version>4.4.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ctrip.hermes</groupId>
                        <artifactId>hermes-kafka</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.71</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.sysdev</groupId>
                <artifactId>herald-token-lib</artifactId>
                <version>1.4.0</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.sd</groupId>
                <artifactId>carcache-all</artifactId>
                <version>${carcache.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ctrip.soa.car.osd.notificationcenter.v1</groupId>
                        <artifactId>carosdnotificationcenter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.sd</groupId>
                <artifactId>carcache-common</artifactId>
                <version>${carcache.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.soa.car.osd.carosdbasicdataservice.v1</groupId>
                <artifactId>carosdbasicdataservice</artifactId>
                <version>0.0.17</version>
                <exclusions>
                    <exclusion>
                        <artifactId>dto</artifactId>
                        <groupId>com.ctrip.car.osd.framework</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.8</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.customer</groupId>
                <artifactId>customer-common</artifactId>
                <version>1.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.ibu.platform</groupId>
                <artifactId>ibu-shark-sdk</artifactId>
                <version>5.1.3</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.flight.intl.common</groupId>
                <artifactId>metric-client</artifactId>
                <version>4.0.8</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>market-crossrecommend-contract</artifactId>
                <version>1.0.12</version>
            </dependency>
            <!--翻译-->
            <dependency>
                <groupId>com.ctrip.car.osd.translate</groupId>
                <artifactId>client</artifactId>
                <version>1.0.5</version>
                <exclusions>
                    <exclusion>
                        <artifactId>dto</artifactId>
                        <groupId>com.ctrip.car.osd.framework</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--汇率-->
            <dependency>
                <groupId>com.ctrip.soa.platform.accounting.exchangerateservice.v1</groupId>
                <artifactId>exchangerateservice</artifactId>
                <version>0.0.4</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.tour.ai</groupId>
                <artifactId>one-service-client</artifactId>
                <version>${one.service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.soa.car.order.sdneworderservice.v1</groupId>
                <artifactId>sdneworderservice</artifactId>
                <version>${sdneworderservice.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.basebiz</groupId>
                <artifactId>account-service-client</artifactId>
                <version>${account.service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>car-market-common</artifactId>
                <version>${market.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.payment.cwallet</groupId>
                <artifactId>caccount-base-tx-service-client</artifactId>
                <version>${payment.cwallet.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.risk</groupId>
                <artifactId>safetrip-unified-access-api</artifactId>
                <version>${safetrip.unified.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.ctrip.framework</groupId>
            <artifactId>triplog-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.agent</artifactId>
            <classifier>runtime</classifier>
        </dependency>

        <dependency>
            <groupId>org.codehaus.sonar-plugins.java</groupId>
            <artifactId>sonar-jacoco-listeners</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--对象转换-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.objenesis</groupId>
                    <artifactId>objenesis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.objenesis</groupId>
            <artifactId>objenesis</artifactId>
            <version>2.5.1</version>
        </dependency>

        <!--  spock 的核心包 -->
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <version>1.0-groovy-2.4</version>
            <scope>test</scope>
        </dependency>
        <!-- spock依赖的groovy -->
        <!-- use a specific Groovy version rather than the one specified by spock-core -->
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>2.4.13</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.dcs.geo</groupId>
            <artifactId>geo-platform-sdk</artifactId>
            <version>1.3.7</version>
            <exclusions>
                <exclusion>
                    <groupId>org.jacoco</groupId>
                    <artifactId>org.jacoco.agent</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.dcs.common</groupId>
                    <artifactId>dcs-common-component-common-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.basicservice</groupId>
            <artifactId>igt-basicservice-client</artifactId>
            <version>1.3.14</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.igt.framework</groupId>
                    <artifactId>soa-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.gs</groupId>
            <artifactId>global-poi-soa-client</artifactId>
            <version>1.4.64</version>
        </dependency>
    </dependencies>

    <build>
        <extensions>
            <extension>
                <groupId>com.ctrip.dcs.go</groupId>
                <artifactId>tars</artifactId>
                <version>3.0.0</version>
            </extension>
        </extensions>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <id>default-instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-restore-instrumented-classes</id>
                        <goals>
                            <goal>restore-instrumented-classes</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19.1</version>
                <configuration>
                    <parallel>methods</parallel>
                    <threadCount>10</threadCount>
                    <!--为处理报错而添加Failed to execute goal org.apache.maven.plugins:maven-surefire-plugin:2.19.1:test (default-test) on project isd-shopping-domain: Execution default-test of goal org.apache.maven.plugins:maven-surefire-plugin:2.19.1:test failed: The forked VM terminated without properly saying goodbye. VM crash or System.exit called?-->
                    <argLine>-Xmx1024m -XX:MaxPermSize=256m</argLine>
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>target/jacoco.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                    <testFailureIgnore>true</testFailureIgnore>
                    <argLine>-Xmx2048m -XX:MaxPermSize=512m</argLine>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.ctrip.ibu.platform</groupId>
                <artifactId>shark-maven-plugin</artifactId>
                <version>1.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>pack-download</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
